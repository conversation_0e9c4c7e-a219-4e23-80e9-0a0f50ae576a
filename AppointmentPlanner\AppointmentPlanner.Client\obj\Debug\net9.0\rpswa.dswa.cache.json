{"GlobalPropertiesHash": "VPLBWIyylkJ6BqN87TFTyVp0uYtaG3MVFgqqodzHXi8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["kk8Ibi0xglNU3qYnCUdY8cxHYJYAljKQFnccJIZn1NE=", "W8XCnBsIeiJB2eBtsTz+H8OSxLBIWfporIhn4riAw8s=", "gQxfyzYxZ8l+tZo0f3RBgLKMPGwGvE20XfKeBKWVD84=", "9e7kCMnptDsuDqGoOPUWUOgAGd4b+VSluLMjZk2ucrI=", "n2w8h5a8uXJSnJpSYkGToc763MEdYKkDXOAkamwIXXc=", "aQQLzmv78SOQ75FMOGCcNMo6ldWDQb65RuL8EJ+wFsQ=", "BVvSqDnL6mqK3bdyhr7J/m87xdIpyCNMiShFSlCuxSg=", "wcJ6uRsHmLBx2uzvF6qkWi0Rgy6CRpYBRj/1Mv3uUGo=", "eV1wGDIBt+9wa4IQyF0OEUOIzGylsF1CbZXZZI7yEOQ=", "VP30UMhzqJEsesXNXSuU2Zvai+RDer4esGK/ehcvwhU=", "+rCCvoqzh4DKERZJPaosBJiYYENGx9g4kFNET8npvdY=", "pt+D9zrJfTmB3RbwALRtdl6yfNr7MHD5ua087jhZYbo=", "RKODFEWwxowckFU2cZ7ZYX0BuhOO/RCE1zTBsKAr11o="], "CachedAssets": {"kk8Ibi0xglNU3qYnCUdY8cxHYJYAljKQFnccJIZn1NE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.Development.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "W8XCnBsIeiJB2eBtsTz+H8OSxLBIWfporIhn4riAw8s=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}}, "CachedCopyCandidates": {}}