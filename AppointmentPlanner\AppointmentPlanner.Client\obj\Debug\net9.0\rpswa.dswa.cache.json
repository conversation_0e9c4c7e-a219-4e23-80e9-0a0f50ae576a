{"GlobalPropertiesHash": "VPLBWIyylkJ6BqN87TFTyVp0uYtaG3MVFgqqodzHXi8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["kk8Ibi0xglNU3qYnCUdY8cxHYJYAljKQFnccJIZn1NE=", "W8XCnBsIeiJB2eBtsTz+H8OSxLBIWfporIhn4riAw8s=", "YAS6Dxitm7isKkfSnNJhashInGEdXk1V2hNh8q77pu4=", "gQxfyzYxZ8l+tZo0f3RBgLKMPGwGvE20XfKeBKWVD84=", "9e7kCMnptDsuDqGoOPUWUOgAGd4b+VSluLMjZk2ucrI=", "hDslN5Lfc7cIikri50LMCeVEE5c72uECGssNLh6zdj0=", "66pbJ+d8KZ3w9ekx4lvVdjjBCrubOiyu/YTm0QpKE6U=", "Nkf3oCN3XI05RF8vIr88RnkLuFQfhOwlNrg+86m+c2w=", "/kGHeJg9uECL1P2F+Zc2cjWsFIjI3dgap3f6OUdteYM=", "n2w8h5a8uXJSnJpSYkGToc763MEdYKkDXOAkamwIXXc=", "aQQLzmv78SOQ75FMOGCcNMo6ldWDQb65RuL8EJ+wFsQ=", "BVvSqDnL6mqK3bdyhr7J/m87xdIpyCNMiShFSlCuxSg=", "wcJ6uRsHmLBx2uzvF6qkWi0Rgy6CRpYBRj/1Mv3uUGo=", "eV1wGDIBt+9wa4IQyF0OEUOIzGylsF1CbZXZZI7yEOQ=", "ckUtMimokbestvTP7QIOe92JGw1SWD3GbT3dC0cvv/Y=", "VP30UMhzqJEsesXNXSuU2Zvai+RDer4esGK/ehcvwhU=", "CsrRxgZ/r4TD+xn9nRtopGQk9SO4CfV4GVwymJ5Z+Oc=", "9/MiU94Woc1/h2inPxCJmht+qN5s2Wj45FbvYS5UkJ4=", "pt+D9zrJfTmB3RbwALRtdl6yfNr7MHD5ua087jhZYbo=", "YIEXHvf3SWhUKG8Hc8WaZ6umLlhrpQjO3mzI4zPfRgs="], "CachedAssets": {"kk8Ibi0xglNU3qYnCUdY8cxHYJYAljKQFnccJIZn1NE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.Development.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "W8XCnBsIeiJB2eBtsTz+H8OSxLBIWfporIhn4riAw8s=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "YAS6Dxitm7isKkfSnNJhashInGEdXk1V2hNh8q77pu4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\css\\auth.css", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/auth#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "twhnronm6j", "Integrity": "k/q1l+5XaPed/kVQVUY35/RI+YmJ27I4UlGckH9tA30=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\auth.css", "FileLength": 5922, "LastWriteTime": "2025-07-14T20:05:52.6198292+00:00"}}, "CachedCopyCandidates": {}}