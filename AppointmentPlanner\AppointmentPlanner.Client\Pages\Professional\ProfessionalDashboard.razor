@page "/doctor/dashboard"
@page "/professional/dashboard"
@attribute [Authorize(Roles = "Professional")]
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization

<PageTitle>Professional Dashboard - Appointment Planner</PageTitle>

<div class="professional-dashboard">
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-user-md"></i>
            Professional Dashboard
        </h1>
        <p class="dashboard-subtitle">Manage your appointments and patient care</p>
    </div>

    <div class="row">
        <!-- Today's Statistics -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-primary">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <h3>@todayAppointments</h3>
                    <p>Today's Appointments</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>@completedToday</h3>
                    <p>Completed Today</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>@pendingAppointments</h3>
                    <p>Pending</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-info">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>@totalPatients</h3>
                    <p>Total Patients</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Today's Schedule -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-calendar-alt"></i>
                        Today's Schedule
                    </h5>
                </div>
                <div class="card-body">
                    <div class="schedule-list">
                        @foreach (var appointment in todaySchedule)
                        {
                            <div class="schedule-item @appointment.StatusClass">
                                <div class="schedule-time">
                                    <strong>@appointment.StartTime.ToString("HH:mm")</strong>
                                    <small>@appointment.StartTime.ToString("HH:mm") - @appointment.EndTime.ToString("HH:mm")</small>
                                </div>
                                <div class="schedule-content">
                                    <h6 class="patient-name">@appointment.PatientName</h6>
                                    <p class="appointment-type">@appointment.Type</p>
                                    <small class="appointment-notes">@appointment.Notes</small>
                                </div>
                                <div class="schedule-actions">
                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewPatient(appointment.Id)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    @if (appointment.Status == "Scheduled")
                                    {
                                        <button class="btn btn-sm btn-success" @onclick="() => StartAppointment(appointment.Id)">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    }
                                    else if (appointment.Status == "InProgress")
                                    {
                                        <button class="btn btn-sm btn-warning" @onclick="() => CompleteAppointment(appointment.Id)">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Profile -->
        <div class="col-lg-4 mb-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <button class="btn btn-primary btn-action" @onclick="ViewFullSchedule">
                            <i class="fas fa-calendar"></i>
                            Full Schedule
                        </button>
                        <button class="btn btn-success btn-action" @onclick="ManagePatients">
                            <i class="fas fa-users"></i>
                            My Patients
                        </button>
                        <button class="btn btn-info btn-action" @onclick="UpdateProfile">
                            <i class="fas fa-user-edit"></i>
                            Update Profile
                        </button>
                        <button class="btn btn-warning btn-action" @onclick="ViewReports">
                            <i class="fas fa-chart-line"></i>
                            Reports
                        </button>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-user-circle"></i>
                        Professional Profile
                    </h5>
                </div>
                <div class="card-body">
                    <div class="profile-info">
                        <div class="profile-avatar">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div class="profile-details">
                            <h6>Dr. John Smith</h6>
                            <p class="specialization">Cardiologist</p>
                            <p class="experience">15+ years experience</p>
                            <p class="department">Cardiology Department</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Patients -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-history"></i>
                        Recent Patients
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Professional Features:</strong> Patient management, appointment scheduling, medical records access, and treatment history tracking.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .professional-dashboard {
        padding: 20px;
    }

    .dashboard-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .dashboard-title {
        color: #333;
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .dashboard-title i {
        color: #28a745;
        margin-right: 15px;
    }

    .dashboard-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .stat-primary .stat-icon { background: #007bff; }
    .stat-success .stat-icon { background: #28a745; }
    .stat-info .stat-icon { background: #17a2b8; }
    .stat-warning .stat-icon { background: #ffc107; }

    .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        color: #333;
    }

    .stat-content p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .schedule-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .schedule-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        border-left: 4px solid #e9ecef;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 8px 8px 0;
        transition: all 0.3s ease;
    }

    .schedule-item.scheduled {
        border-left-color: #007bff;
        background: #f0f8ff;
    }

    .schedule-item.in-progress {
        border-left-color: #ffc107;
        background: #fffbf0;
    }

    .schedule-item.completed {
        border-left-color: #28a745;
        background: #f0fff4;
    }

    .schedule-time {
        min-width: 80px;
        text-align: center;
    }

    .schedule-time strong {
        display: block;
        color: #333;
        font-size: 1.1rem;
    }

    .schedule-time small {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .schedule-content {
        flex: 1;
    }

    .patient-name {
        margin: 0 0 5px 0;
        color: #333;
        font-weight: 600;
    }

    .appointment-type {
        margin: 0 0 5px 0;
        color: #007bff;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .appointment-notes {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .schedule-actions {
        display: flex;
        gap: 5px;
    }

    .quick-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .btn-action {
        padding: 12px 15px;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-start;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateX(5px);
    }

    .profile-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .profile-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #28a745;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .profile-details h6 {
        margin: 0 0 5px 0;
        color: #333;
        font-weight: 600;
    }

    .profile-details p {
        margin: 0 0 3px 0;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .specialization {
        color: #007bff !important;
        font-weight: 500 !important;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 10px 10px 0 0 !important;
    }

    .card-title {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .card-title i {
        color: #28a745;
        margin-right: 10px;
    }
</style>

@code {
    private int todayAppointments = 8;
    private int completedToday = 3;
    private int pendingAppointments = 5;
    private int totalPatients = 142;

    private List<ScheduleItem> todaySchedule = new()
    {
        new ScheduleItem { Id = 1, PatientName = "John Doe", Type = "Regular Checkup", StartTime = DateTime.Today.AddHours(9), EndTime = DateTime.Today.AddHours(9.5), Status = "Completed", StatusClass = "completed", Notes = "Routine cardiac examination" },
        new ScheduleItem { Id = 2, PatientName = "Jane Smith", Type = "Follow-up", StartTime = DateTime.Today.AddHours(10), EndTime = DateTime.Today.AddHours(10.5), Status = "InProgress", StatusClass = "in-progress", Notes = "Post-surgery follow-up" },
        new ScheduleItem { Id = 3, PatientName = "Mike Johnson", Type = "Consultation", StartTime = DateTime.Today.AddHours(11), EndTime = DateTime.Today.AddHours(11.5), Status = "Scheduled", StatusClass = "scheduled", Notes = "Chest pain consultation" },
        new ScheduleItem { Id = 4, PatientName = "Sarah Wilson", Type = "Emergency", StartTime = DateTime.Today.AddHours(14), EndTime = DateTime.Today.AddHours(14.5), Status = "Scheduled", StatusClass = "scheduled", Notes = "Urgent cardiac assessment" }
    };

    private void ViewPatient(int appointmentId)
    {
        // Navigate to patient details
    }

    private void StartAppointment(int appointmentId)
    {
        // Start appointment
    }

    private void CompleteAppointment(int appointmentId)
    {
        // Complete appointment
    }

    private void ViewFullSchedule()
    {
        // Navigate to full schedule
    }

    private void ManagePatients()
    {
        // Navigate to patient management
    }

    private void UpdateProfile()
    {
        // Navigate to profile update
    }

    private void ViewReports()
    {
        // Navigate to reports
    }

    public class ScheduleItem
    {
        public int Id { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Status { get; set; } = string.Empty;
        public string StatusClass { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
    }
}
