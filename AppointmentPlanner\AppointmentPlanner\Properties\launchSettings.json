{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:10682", "sslPort": 44326}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "AppointmentPlanner": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "applicationUrl": "https://localhost:7013;http://localhost:5204", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}