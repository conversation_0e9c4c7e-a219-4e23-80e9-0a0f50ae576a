{"version": 3, "targets": {"net9.0": {"Microsoft.AspNetCore.Authorization/9.0.6": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/9.0.6": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.6", "Microsoft.AspNetCore.Components.Analyzers": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "9.0.6", "Microsoft.AspNetCore.Components.Forms": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "Microsoft.JSInterop": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.6": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.JSInterop.WebAssembly": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net9.0/_._": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"build/Microsoft.AspNetCore.Components.WebAssembly.Server.targets": {}}}, "Microsoft.AspNetCore.Metadata/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.JSInterop/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/9.0.6": {"type": "package", "dependencies": {"Microsoft.JSInterop": "9.0.6"}, "compile": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "Syncfusion.Blazor.Buttons/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Buttons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Buttons.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Buttons.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Buttons.props": {}}}, "Syncfusion.Blazor.Calendars/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Lists": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Calendars.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Calendars.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Calendars.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Calendars.props": {}}}, "Syncfusion.Blazor.Charts/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DataVizCommon": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35", "Syncfusion.PdfExport.Net.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Charts.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Charts.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Charts.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Charts.props": {}}}, "Syncfusion.Blazor.Core/26.1.35": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.0", "Syncfusion.Licensing": "26.1.35", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Core.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Core.props": {}}}, "Syncfusion.Blazor.Data/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Data.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Data.props": {}}}, "Syncfusion.Blazor.DataVizCommon/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.DataVizCommon.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DataVizCommon.dll": {"related": ".xml"}}}, "Syncfusion.Blazor.DropDowns/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Notifications": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.DropDowns.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DropDowns.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.DropDowns.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.DropDowns.props": {}}}, "Syncfusion.Blazor.Grid/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Calendars": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35", "Syncfusion.PdfExport.Net.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Grids.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Grids.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Grid.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Grid.props": {}}}, "Syncfusion.Blazor.Inputs/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.Blazor.SplitButtons": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Inputs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Inputs.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Inputs.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Inputs.props": {}}}, "Syncfusion.Blazor.Lists/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Lists.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Lists.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Lists.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Lists.props": {}}}, "Syncfusion.Blazor.Navigations/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Lists": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Navigations.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Navigations.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Navigations.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Navigations.props": {}}}, "Syncfusion.Blazor.Notifications/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Notifications.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Notifications.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Notifications.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Notifications.props": {}}}, "Syncfusion.Blazor.Popups/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Popups.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Popups.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Popups.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Popups.props": {}}}, "Syncfusion.Blazor.Schedule/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Calendars": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Schedule.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Schedule.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Schedule.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Schedule.props": {}}}, "Syncfusion.Blazor.Spinner/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.Spinner.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Spinner.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Spinner.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Spinner.props": {}}}, "Syncfusion.Blazor.SplitButtons/26.1.35": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "compile": {"lib/net8.0/Syncfusion.Blazor.SplitButtons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.SplitButtons.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.SplitButtons.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.SplitButtons.props": {}}}, "Syncfusion.Blazor.Themes/26.1.35": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Blazor.Themes.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Themes.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Themes.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Themes.props": {}}}, "Syncfusion.ExcelExport.Net.Core/26.1.35": {"type": "package", "compile": {"lib/net8.0/Syncfusion.ExcelExport.Net.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.ExcelExport.Net.dll": {"related": ".xml"}}}, "Syncfusion.Licensing/26.1.35": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Licensing.dll": {}}, "runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {}}}, "Syncfusion.PdfExport.Net.Core/26.1.35": {"type": "package", "compile": {"lib/net8.0/Syncfusion.PdfExport.Net.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.PdfExport.Net.dll": {"related": ".xml"}}}, "System.Text.Json/9.0.6": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "AppointmentPlanner.Client/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": "9.0.6", "Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Charts": "26.1.35", "Syncfusion.Blazor.Grid": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Notifications": "26.1.35", "Syncfusion.Blazor.Schedule": "26.1.35", "Syncfusion.Blazor.Themes": "26.1.35", "System.Text.Json": "9.0.6"}, "compile": {"bin/placeholder/AppointmentPlanner.Client.dll": {}}, "runtime": {"bin/placeholder/AppointmentPlanner.Client.dll": {}}}}}, "libraries": {"Microsoft.AspNetCore.Authorization/9.0.6": {"sha512": "FG/fHZAg2J9NKt+y2BH+fMjc+TqA7wlri/3PNtoSgvclMlr5iPshDLXHQgvS9l7IUzzfkqQ3/DWPYuA4OJu75A==", "type": "package", "path": "microsoft.aspnetcore.authorization/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net9.0/Microsoft.AspNetCore.Authorization.dll", "lib/net9.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.9.0.6.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/9.0.6": {"sha512": "4WzUBnpxqm0Gvv3XX47h5g09LN4dtQpJjYW2LAfoaVeho5TLM00uDj2YHzjxQwcRTdrG7tZDCyf4V4wwRpjGcA==", "type": "package", "path": "microsoft.aspnetcore.components/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.dll", "lib/net9.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.9.0.6.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {"sha512": "UXMjbezwMMoL21L20OB1BLGF9M1BFPzdWiezaiZSBG55z36BEXvs8hetKw7PiuytOdUqWFI1wkPd3MD9jpeKAw==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "microsoft.aspnetcore.components.analyzers.9.0.6.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"sha512": "GHdFdrU+9ePkuE7S2g7iu+Lg8FPBY9qGaK+FPSBHNx8ulEdrOHJhufb1FZH/m++NoJgcikOzJ14+eL96HfBr8g==", "type": "package", "path": "microsoft.aspnetcore.components.forms/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.9.0.6.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"sha512": "c+2B1DdgAOKXt+mMcZgeQPbdoW0Ro+783m+yPK/QQHhjV8cbkPBWXg6OMJC2Vzum5tfAC3coNhlmcLUUBwc3ZQ==", "type": "package", "path": "microsoft.aspnetcore.components.web/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Web.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.9.0.6.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.6": {"sha512": "w0n0ge6caIRDhMn03/OGkBWJPFOrf/VS4UNSdbm7hibbAG9Y9oFUnkY6DN+b3WgXcjprSep1VypqC83dzGskXw==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/net9.0/Microsoft.AspNetCore.Components.WebAssembly.props", "build/net9.0/blazor.webassembly.js", "lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll", "lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.xml", "microsoft.aspnetcore.components.webassembly.9.0.6.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.6": {"sha512": "e8Y0+dSBpsfdYXUgBCHrzAeFywRFa5aog7O8l/VFwSAQCJJlcOKOus/RGRVHTLBARGdHfr9guSdMtSYXjFGOJg==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly.server/9.0.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/Microsoft.AspNetCore.Components.WebAssembly.Server.targets", "lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll", "lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.xml", "microsoft.aspnetcore.components.webassembly.server.9.0.6.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.server.nuspec", "tools/BlazorDebugProxy/BrowserDebugHost.dll", "tools/BlazorDebugProxy/BrowserDebugHost.runtimeconfig.json", "tools/BlazorDebugProxy/BrowserDebugProxy.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.Scripting.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.dll", "tools/BlazorDebugProxy/Microsoft.FileFormats.dll", "tools/BlazorDebugProxy/Microsoft.NET.WebAssembly.Webcil.dll", "tools/BlazorDebugProxy/Microsoft.SymbolStore.dll", "tools/BlazorDebugProxy/Newtonsoft.Json.dll"]}, "Microsoft.AspNetCore.Metadata/9.0.6": {"sha512": "SZAmFKGsQPP/xyDir96J5UNTAdCsfeCpPwFO9EewIcgQ8gUr3KHmn4CTC2HmHIDdiTujGC2T3VBcSxmrY+3n/g==", "type": "package", "path": "microsoft.aspnetcore.metadata/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net9.0/Microsoft.AspNetCore.Metadata.dll", "lib/net9.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.9.0.6.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.Extensions.Configuration/9.0.6": {"sha512": "VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "type": "package", "path": "microsoft.extensions.configuration/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"sha512": "3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"sha512": "Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"sha512": "pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"sha512": "N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"sha512": "vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"sha512": "q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"sha512": "l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"sha512": "1HJCAbwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.6": {"sha512": "XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "type": "package", "path": "microsoft.extensions.logging/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.6.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"sha512": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.6": {"sha512": "wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "type": "package", "path": "microsoft.extensions.options/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.6.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.6": {"sha512": "BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "type": "package", "path": "microsoft.extensions.primitives/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.6.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.JSInterop/9.0.6": {"sha512": "IS9tI2pnpeoX+d4CO4FuTsm+6uymanPGSpBhHefTfMNIwBUvwLIcXch4su0QEW4MSslYdDJ9yzz2+OuIUuI5lw==", "type": "package", "path": "microsoft.jsinterop/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.JSInterop.dll", "lib/net9.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.9.0.6.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Microsoft.JSInterop.WebAssembly/9.0.6": {"sha512": "y93mMbFyYo5lq9cDkm2wYlob7RmeKulZVpjMYUOIztry/TpXlHrFgBb2TcWMuKnwvfbf4Ag6oIx+F3aQAAVA6A==", "type": "package", "path": "microsoft.jsinterop.webassembly/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.JSInterop.WebAssembly.dll", "lib/net9.0/Microsoft.JSInterop.WebAssembly.xml", "microsoft.jsinterop.webassembly.9.0.6.nupkg.sha512", "microsoft.jsinterop.webassembly.nuspec"]}, "Syncfusion.Blazor.Buttons/26.1.35": {"sha512": "sky/UkU6X/9Jf7bPyv/ltYmrLGhzttv3zbw0o0F/+76Hr/A0TNXn4Bv6fCP0eR3o4qTm7i9EH9qXWbcp5Oz5JQ==", "type": "package", "path": "syncfusion.blazor.buttons/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Buttons.props", "buildMultiTargeting/Syncfusion.Blazor.Buttons.props", "buildTransitive/Syncfusion.Blazor.Buttons.props", "lib/net6.0/Syncfusion.Blazor.Buttons.dll", "lib/net6.0/Syncfusion.Blazor.Buttons.xml", "lib/net7.0/Syncfusion.Blazor.Buttons.dll", "lib/net7.0/Syncfusion.Blazor.Buttons.xml", "lib/net8.0/Syncfusion.Blazor.Buttons.dll", "lib/net8.0/Syncfusion.Blazor.Buttons.xml", "staticwebassets/scripts/sf-floating-action-button.min.js", "staticwebassets/scripts/sf-speeddial.min.js", "syncfusion.blazor.buttons.26.1.35.nupkg.sha512", "syncfusion.blazor.buttons.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Calendars/26.1.35": {"sha512": "AmchDdNrcOtLvvXLXxVVrBpbE3jJlezzlRnw8GBe0zE2vd2fOllfYgjHPKzQywrZqDTALF/REjBXhRn4PyRLeA==", "type": "package", "path": "syncfusion.blazor.calendars/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Calendars.props", "buildMultiTargeting/Syncfusion.Blazor.Calendars.props", "buildTransitive/Syncfusion.Blazor.Calendars.props", "lib/net6.0/Syncfusion.Blazor.Calendars.dll", "lib/net6.0/Syncfusion.Blazor.Calendars.xml", "lib/net7.0/Syncfusion.Blazor.Calendars.dll", "lib/net7.0/Syncfusion.Blazor.Calendars.xml", "lib/net8.0/Syncfusion.Blazor.Calendars.dll", "lib/net8.0/Syncfusion.Blazor.Calendars.xml", "staticwebassets/scripts/sf-calendar.min.js", "staticwebassets/scripts/sf-datepicker.min.js", "staticwebassets/scripts/sf-daterangepicker.min.js", "staticwebassets/scripts/sf-timepicker.min.js", "syncfusion.blazor.calendars.26.1.35.nupkg.sha512", "syncfusion.blazor.calendars.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Charts/26.1.35": {"sha512": "j1uEzmu1JEI6xSh0TMWWjmvusGdE5VPKHLpm1hwVd9KmN5jKMvXmHmmDOEkyozYJNDQ6DaPWkDYCMJuC/waLSg==", "type": "package", "path": "syncfusion.blazor.charts/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Charts.props", "buildMultiTargeting/Syncfusion.Blazor.Charts.props", "buildTransitive/Syncfusion.Blazor.Charts.props", "lib/net6.0/Syncfusion.Blazor.Charts.dll", "lib/net6.0/Syncfusion.Blazor.Charts.xml", "lib/net7.0/Syncfusion.Blazor.Charts.dll", "lib/net7.0/Syncfusion.Blazor.Charts.xml", "lib/net8.0/Syncfusion.Blazor.Charts.dll", "lib/net8.0/Syncfusion.Blazor.Charts.xml", "staticwebassets/scripts/sf-accumulation-chart.min.js", "staticwebassets/scripts/sf-chart.min.js", "syncfusion.blazor.charts.26.1.35.nupkg.sha512", "syncfusion.blazor.charts.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Core/26.1.35": {"sha512": "aQDiHkPJLAqn4qJDXM7b0fk9+D4+mbyWmlOl4NJ4YrXhMoy2QB0yWCv6QnHkPS6PZmOeqCByybT69LW/rm5x5g==", "type": "package", "path": "syncfusion.blazor.core/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Core.props", "buildMultiTargeting/Syncfusion.Blazor.Core.props", "buildTransitive/Syncfusion.Blazor.Core.props", "lib/net6.0/Syncfusion.Blazor.Core.dll", "lib/net6.0/Syncfusion.Blazor.Core.xml", "lib/net7.0/Syncfusion.Blazor.Core.dll", "lib/net7.0/Syncfusion.Blazor.Core.xml", "lib/net8.0/Syncfusion.Blazor.Core.dll", "lib/net8.0/Syncfusion.Blazor.Core.xml", "staticwebassets/scripts/popup.min.js", "staticwebassets/scripts/popupsbase.min.js", "staticwebassets/scripts/sf-svg-export.min.js", "staticwebassets/scripts/svgbase.min.js", "staticwebassets/scripts/syncfusion-blazor-base.min.js", "staticwebassets/scripts/syncfusion-blazor.min.js", "syncfusion.blazor.core.26.1.35.nupkg.sha512", "syncfusion.blazor.core.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Data/26.1.35": {"sha512": "MzTSFeTLYrEjr9ONbS9qzUKaE0554CKAsKHRcY2dj3H+vkJf7XChHLZhswWkQlkyrLR7LlMe5TCxK/LiNJ7adg==", "type": "package", "path": "syncfusion.blazor.data/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Data.props", "buildMultiTargeting/Syncfusion.Blazor.Data.props", "buildTransitive/Syncfusion.Blazor.Data.props", "lib/net6.0/Syncfusion.Blazor.Data.dll", "lib/net6.0/Syncfusion.Blazor.Data.xml", "lib/net7.0/Syncfusion.Blazor.Data.dll", "lib/net7.0/Syncfusion.Blazor.Data.xml", "lib/net8.0/Syncfusion.Blazor.Data.dll", "lib/net8.0/Syncfusion.Blazor.Data.xml", "staticwebassets/scripts/data.min.js", "syncfusion.blazor.data.26.1.35.nupkg.sha512", "syncfusion.blazor.data.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.DataVizCommon/26.1.35": {"sha512": "I01SzntfWFPsju0kurhSgPs+mZAZXIDwKyyXVJdqOYRgEsBZqTksWRHTBjpc8PLI0r+QQd213F9Pa5/8sSZ+9A==", "type": "package", "path": "syncfusion.blazor.datavizcommon/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0/Syncfusion.Blazor.DataVizCommon.dll", "lib/net6.0/Syncfusion.Blazor.DataVizCommon.xml", "lib/net7.0/Syncfusion.Blazor.DataVizCommon.dll", "lib/net7.0/Syncfusion.Blazor.DataVizCommon.xml", "lib/net8.0/Syncfusion.Blazor.DataVizCommon.dll", "lib/net8.0/Syncfusion.Blazor.DataVizCommon.xml", "syncfusion.blazor.datavizcommon.26.1.35.nupkg.sha512", "syncfusion.blazor.datavizcommon.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.DropDowns/26.1.35": {"sha512": "MmIzaF3yeqlqLXuMNiLgW5XmQHyV6PLmPVZ9gIBq9ILTacPxiMYqbXTYAGFDQDAnDpsxe6QTZqgqXAD+Sex1Tg==", "type": "package", "path": "syncfusion.blazor.dropdowns/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.DropDowns.props", "buildMultiTargeting/Syncfusion.Blazor.DropDowns.props", "buildTransitive/Syncfusion.Blazor.DropDowns.props", "lib/net6.0/Syncfusion.Blazor.DropDowns.dll", "lib/net6.0/Syncfusion.Blazor.DropDowns.xml", "lib/net7.0/Syncfusion.Blazor.DropDowns.dll", "lib/net7.0/Syncfusion.Blazor.DropDowns.xml", "lib/net8.0/Syncfusion.Blazor.DropDowns.dll", "lib/net8.0/Syncfusion.Blazor.DropDowns.xml", "staticwebassets/scripts/sf-dropdownlist.min.js", "staticwebassets/scripts/sf-listbox.min.js", "staticwebassets/scripts/sf-mention.min.js", "staticwebassets/scripts/sf-multiselect.min.js", "staticwebassets/scripts/sortable.min.js", "syncfusion.blazor.dropdowns.26.1.35.nupkg.sha512", "syncfusion.blazor.dropdowns.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Grid/26.1.35": {"sha512": "keqIJLIfCxbXbNo0aSn4ZdB0AfcBV1R8JDyNTMcjBugGXJu0nW9s6bbc8iy1u67xAmmvYvFhAANbtzdg8ASvvg==", "type": "package", "path": "syncfusion.blazor.grid/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Grid.props", "buildMultiTargeting/Syncfusion.Blazor.Grid.props", "buildTransitive/Syncfusion.Blazor.Grid.props", "lib/net6.0/Syncfusion.Blazor.Grids.dll", "lib/net6.0/Syncfusion.Blazor.Grids.xml", "lib/net7.0/Syncfusion.Blazor.Grids.dll", "lib/net7.0/Syncfusion.Blazor.Grids.xml", "lib/net8.0/Syncfusion.Blazor.Grids.dll", "lib/net8.0/Syncfusion.Blazor.Grids.xml", "staticwebassets/scripts/sf-grid.min.js", "syncfusion.blazor.grid.26.1.35.nupkg.sha512", "syncfusion.blazor.grid.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Inputs/26.1.35": {"sha512": "QD9CtUriUEDzfU1psaZdhoSSZu5toRlkJCqXJ4VeDaJ424fIU5yxR3QH148gGC19bwCpMraJ2nfUYznwhWgpOQ==", "type": "package", "path": "syncfusion.blazor.inputs/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Inputs.props", "buildMultiTargeting/Syncfusion.Blazor.Inputs.props", "buildTransitive/Syncfusion.Blazor.Inputs.props", "lib/net6.0/Syncfusion.Blazor.Inputs.dll", "lib/net6.0/Syncfusion.Blazor.Inputs.xml", "lib/net7.0/Syncfusion.Blazor.Inputs.dll", "lib/net7.0/Syncfusion.Blazor.Inputs.xml", "lib/net8.0/Syncfusion.Blazor.Inputs.dll", "lib/net8.0/Syncfusion.Blazor.Inputs.xml", "staticwebassets/scripts/sf-colorpicker.min.js", "staticwebassets/scripts/sf-maskedtextbox.min.js", "staticwebassets/scripts/sf-numerictextbox.min.js", "staticwebassets/scripts/sf-otp-input.min.js", "staticwebassets/scripts/sf-rating.min.js", "staticwebassets/scripts/sf-signature.min.js", "staticwebassets/scripts/sf-slider.min.js", "staticwebassets/scripts/sf-textarea.min.js", "staticwebassets/scripts/sf-textbox.min.js", "staticwebassets/scripts/sf-uploader.min.js", "syncfusion.blazor.inputs.26.1.35.nupkg.sha512", "syncfusion.blazor.inputs.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Lists/26.1.35": {"sha512": "jTyeyGf/7j+U3sHshjN8ASj+xAiy4xZLqFTQbIlrv4vrloXU9nZJ4oqZdT420Rp7d3O8iQ66akXytUD61c/sTw==", "type": "package", "path": "syncfusion.blazor.lists/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Lists.props", "buildMultiTargeting/Syncfusion.Blazor.Lists.props", "buildTransitive/Syncfusion.Blazor.Lists.props", "lib/net6.0/Syncfusion.Blazor.Lists.dll", "lib/net6.0/Syncfusion.Blazor.Lists.xml", "lib/net7.0/Syncfusion.Blazor.Lists.dll", "lib/net7.0/Syncfusion.Blazor.Lists.xml", "lib/net8.0/Syncfusion.Blazor.Lists.dll", "lib/net8.0/Syncfusion.Blazor.Lists.xml", "staticwebassets/scripts/sf-listview.min.js", "syncfusion.blazor.lists.26.1.35.nupkg.sha512", "syncfusion.blazor.lists.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Navigations/26.1.35": {"sha512": "zoxCVQBdcVBMfIc2grIXX9w7UjbcWoHSiGGn+suzzNuGUknHoO3oTQJWUWjZO4MtCziSsooy0DdmuWaNsBKgFQ==", "type": "package", "path": "syncfusion.blazor.navigations/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Navigations.props", "buildMultiTargeting/Syncfusion.Blazor.Navigations.props", "buildTransitive/Syncfusion.Blazor.Navigations.props", "lib/net6.0/Syncfusion.Blazor.Navigations.dll", "lib/net6.0/Syncfusion.Blazor.Navigations.xml", "lib/net7.0/Syncfusion.Blazor.Navigations.dll", "lib/net7.0/Syncfusion.Blazor.Navigations.xml", "lib/net8.0/Syncfusion.Blazor.Navigations.dll", "lib/net8.0/Syncfusion.Blazor.Navigations.xml", "staticwebassets/scripts/navigationsbase.min.js", "staticwebassets/scripts/sf-accordion.min.js", "staticwebassets/scripts/sf-breadcrumb.min.js", "staticwebassets/scripts/sf-carousel.min.js", "staticwebassets/scripts/sf-contextmenu.min.js", "staticwebassets/scripts/sf-dropdowntree.min.js", "staticwebassets/scripts/sf-menu.min.js", "staticwebassets/scripts/sf-pager.min.js", "staticwebassets/scripts/sf-sidebar.min.js", "staticwebassets/scripts/sf-stepper.min.js", "staticwebassets/scripts/sf-tab.min.js", "staticwebassets/scripts/sf-toolbar.min.js", "staticwebassets/scripts/sf-treeview.min.js", "syncfusion.blazor.navigations.26.1.35.nupkg.sha512", "syncfusion.blazor.navigations.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Notifications/26.1.35": {"sha512": "UjpQvqsvELyqRYu6mKyqUowds54Xdmsh796cmRIOo4hHKq4ruH7N4Nr5Wdu6EI9AHcHlr9WMo8Ti3Dtz/nntcg==", "type": "package", "path": "syncfusion.blazor.notifications/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Notifications.props", "buildMultiTargeting/Syncfusion.Blazor.Notifications.props", "buildTransitive/Syncfusion.Blazor.Notifications.props", "lib/net6.0/Syncfusion.Blazor.Notifications.dll", "lib/net6.0/Syncfusion.Blazor.Notifications.xml", "lib/net7.0/Syncfusion.Blazor.Notifications.dll", "lib/net7.0/Syncfusion.Blazor.Notifications.xml", "lib/net8.0/Syncfusion.Blazor.Notifications.dll", "lib/net8.0/Syncfusion.Blazor.Notifications.xml", "staticwebassets/scripts/sf-toast.min.js", "syncfusion.blazor.notifications.26.1.35.nupkg.sha512", "syncfusion.blazor.notifications.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Popups/26.1.35": {"sha512": "SHrSVZr/ahrbBfe+i+8uh3YiFvQwAYrcH2tqV2HQDvjJwDhjRt0lLuLjl1CzzQFSF4/CDFHKsoF2eZ8CC5N5dw==", "type": "package", "path": "syncfusion.blazor.popups/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Popups.props", "buildMultiTargeting/Syncfusion.Blazor.Popups.props", "buildTransitive/Syncfusion.Blazor.Popups.props", "lib/net6.0/Syncfusion.Blazor.Popups.dll", "lib/net6.0/Syncfusion.Blazor.Popups.xml", "lib/net7.0/Syncfusion.Blazor.Popups.dll", "lib/net7.0/Syncfusion.Blazor.Popups.xml", "lib/net8.0/Syncfusion.Blazor.Popups.dll", "lib/net8.0/Syncfusion.Blazor.Popups.xml", "staticwebassets/scripts/sf-dialog.min.js", "staticwebassets/scripts/sf-tooltip.min.js", "syncfusion.blazor.popups.26.1.35.nupkg.sha512", "syncfusion.blazor.popups.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Schedule/26.1.35": {"sha512": "G0TF7QtCPlpCAklIYocsWNmAqrp065U2m5W7kqslpUGbBMRDyvSFOveMT84rXv/Ropa3x7Q8Seshzaxiu4wECg==", "type": "package", "path": "syncfusion.blazor.schedule/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Schedule.props", "buildMultiTargeting/Syncfusion.Blazor.Schedule.props", "buildTransitive/Syncfusion.Blazor.Schedule.props", "lib/net6.0/Syncfusion.Blazor.Schedule.dll", "lib/net6.0/Syncfusion.Blazor.Schedule.xml", "lib/net7.0/Syncfusion.Blazor.Schedule.dll", "lib/net7.0/Syncfusion.Blazor.Schedule.xml", "lib/net8.0/Syncfusion.Blazor.Schedule.dll", "lib/net8.0/Syncfusion.Blazor.Schedule.xml", "staticwebassets/scripts/sf-schedule.min.js", "syncfusion.blazor.schedule.26.1.35.nupkg.sha512", "syncfusion.blazor.schedule.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Spinner/26.1.35": {"sha512": "KabpF4Y2dvtlIQ+EEkipwMVOaIqeDhHg+mBtjEvUWXOdy2jwum5KH0oz2C6cJoaDcPyUTGbNdqZ5i/9KUN8W5A==", "type": "package", "path": "syncfusion.blazor.spinner/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Spinner.props", "buildMultiTargeting/Syncfusion.Blazor.Spinner.props", "buildTransitive/Syncfusion.Blazor.Spinner.props", "lib/net6.0/Syncfusion.Blazor.Spinner.dll", "lib/net6.0/Syncfusion.Blazor.Spinner.xml", "lib/net7.0/Syncfusion.Blazor.Spinner.dll", "lib/net7.0/Syncfusion.Blazor.Spinner.xml", "lib/net8.0/Syncfusion.Blazor.Spinner.dll", "lib/net8.0/Syncfusion.Blazor.Spinner.xml", "staticwebassets/scripts/sf-spinner.min.js", "staticwebassets/scripts/spinner.min.js", "syncfusion.blazor.spinner.26.1.35.nupkg.sha512", "syncfusion.blazor.spinner.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.SplitButtons/26.1.35": {"sha512": "aFAkkpybPrrvFhskAjCsgVftC1Qj3vOUFH8JvnnwjyeRCAlcDLIkOoJgPjjyf/SulFWkMxMb2/seKCM3KkHoiQ==", "type": "package", "path": "syncfusion.blazor.splitbuttons/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.SplitButtons.props", "buildMultiTargeting/Syncfusion.Blazor.SplitButtons.props", "buildTransitive/Syncfusion.Blazor.SplitButtons.props", "lib/net6.0/Syncfusion.Blazor.SplitButtons.dll", "lib/net6.0/Syncfusion.Blazor.SplitButtons.xml", "lib/net7.0/Syncfusion.Blazor.SplitButtons.dll", "lib/net7.0/Syncfusion.Blazor.SplitButtons.xml", "lib/net8.0/Syncfusion.Blazor.SplitButtons.dll", "lib/net8.0/Syncfusion.Blazor.SplitButtons.xml", "staticwebassets/scripts/sf-drop-down-button.min.js", "staticwebassets/scripts/splitbuttonsbase.min.js", "syncfusion.blazor.splitbuttons.26.1.35.nupkg.sha512", "syncfusion.blazor.splitbuttons.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Themes/26.1.35": {"sha512": "sG0M2hw1WnCj0jnDQd6WNQrhjcX27XMY8eK/MMK6M9mDv6cSy2WE76WiYrg9k7GPwkKkwU5ijTeoolkiHc9GSQ==", "type": "package", "path": "syncfusion.blazor.themes/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Themes.props", "buildMultiTargeting/Syncfusion.Blazor.Themes.props", "buildTransitive/Syncfusion.Blazor.Themes.props", "lib/net6.0/Syncfusion.Blazor.Themes.dll", "lib/net6.0/Syncfusion.Blazor.Themes.xml", "lib/net7.0/Syncfusion.Blazor.Themes.dll", "lib/net7.0/Syncfusion.Blazor.Themes.xml", "lib/net8.0/Syncfusion.Blazor.Themes.dll", "lib/net8.0/Syncfusion.Blazor.Themes.xml", "staticwebassets/bootstrap-dark.css", "staticwebassets/bootstrap.css", "staticwebassets/bootstrap4.css", "staticwebassets/bootstrap5-dark.css", "staticwebassets/bootstrap5.css", "staticwebassets/customized/material-dark.css", "staticwebassets/customized/material.css", "staticwebassets/customized/tailwind-dark.css", "staticwebassets/customized/tailwind.css", "staticwebassets/fabric-dark.css", "staticwebassets/fabric.css", "staticwebassets/fluent-dark.css", "staticwebassets/fluent.css", "staticwebassets/fluent2-dark.css", "staticwebassets/fluent2.css", "staticwebassets/highcontrast.css", "staticwebassets/material-dark.css", "staticwebassets/material.css", "staticwebassets/material3-dark.css", "staticwebassets/material3.css", "staticwebassets/tailwind-dark.css", "staticwebassets/tailwind.css", "syncfusion.blazor.themes.26.1.35.nupkg.sha512", "syncfusion.blazor.themes.nuspec", "syncfusion_logo.png"]}, "Syncfusion.ExcelExport.Net.Core/26.1.35": {"sha512": "s0XLmtU2Q+nvjik4fTSxPseaWKS36YNRU59c+Hn8Ion6hPia71gOrWaanwux1l7LOpSAOGH8/TFZVyvLc83PPA==", "type": "package", "path": "syncfusion.excelexport.net.core/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0/Syncfusion.ExcelExport.Net.dll", "lib/net6.0/Syncfusion.ExcelExport.Net.xml", "lib/net7.0/Syncfusion.ExcelExport.Net.dll", "lib/net7.0/Syncfusion.ExcelExport.Net.xml", "lib/net8.0/Syncfusion.ExcelExport.Net.dll", "lib/net8.0/Syncfusion.ExcelExport.Net.xml", "lib/netstandard2.0/Syncfusion.ExcelExport.Net.dll", "lib/netstandard2.0/Syncfusion.ExcelExport.Net.xml", "syncfusion.excelexport.net.core.26.1.35.nupkg.sha512", "syncfusion.excelexport.net.core.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Licensing/26.1.35": {"sha512": "DPMS7ODbzaS+S2//uOMW8XL/08AEIMz/TEb113gaXdwga328ozz+4wiQGZBJzjLGglyO1vV+GTUigquDGHWcFg==", "type": "package", "path": "syncfusion.licensing/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/MonoAndroid90/Syncfusion.Licensing.dll", "lib/Xamarin.Mac/Syncfusion.Licensing.dll", "lib/Xamarin.iOS10/Syncfusion.Licensing.dll", "lib/net40/Syncfusion.Licensing.dll", "lib/net45/Syncfusion.Licensing.dll", "lib/net462/Syncfusion.Licensing.dll", "lib/net6.0/Syncfusion.Licensing.dll", "lib/net7.0/Syncfusion.Licensing.dll", "lib/net8.0/Syncfusion.Licensing.dll", "lib/netstandard2.0/Syncfusion.Licensing.dll", "lib/uap10.0/Syncfusion.Licensing.dll", "syncfusion.licensing.26.1.35.nupkg.sha512", "syncfusion.licensing.nuspec", "syncfusion_logo.png"]}, "Syncfusion.PdfExport.Net.Core/26.1.35": {"sha512": "/mqe7z37o/cHmSAjTY+jtjF2gKkL230U+Nk6hjn5tR9INBET2AbhiqIdE/jMduIQ+NqZplcOj5S1bq65jMgq+w==", "type": "package", "path": "syncfusion.pdfexport.net.core/26.1.35", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0/Syncfusion.PdfExport.Net.dll", "lib/net6.0/Syncfusion.PdfExport.Net.xml", "lib/net7.0/Syncfusion.PdfExport.Net.dll", "lib/net7.0/Syncfusion.PdfExport.Net.xml", "lib/net8.0/Syncfusion.PdfExport.Net.dll", "lib/net8.0/Syncfusion.PdfExport.Net.xml", "lib/netstandard2.0/Syncfusion.PdfExport.Net.dll", "lib/netstandard2.0/Syncfusion.PdfExport.Net.xml", "syncfusion.pdfexport.net.core.26.1.35.nupkg.sha512", "syncfusion.pdfexport.net.core.nuspec", "syncfusion_logo.png"]}, "System.Text.Json/9.0.6": {"sha512": "h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "type": "package", "path": "system.text.json/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.6.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "AppointmentPlanner.Client/1.0.0": {"type": "project", "path": "../AppointmentPlanner.Client/AppointmentPlanner.Client.csproj", "msbuildProject": "../AppointmentPlanner.Client/AppointmentPlanner.Client.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["AppointmentPlanner.Client >= 1.0.0", "Microsoft.AspNetCore.Components.WebAssembly.Server >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\AppointmentPlanner.csproj", "projectName": "AppointmentPlanner", "projectPath": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\AppointmentPlanner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\AppointmentPlanner.Client.csproj": {"projectPath": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner.Client\\AppointmentPlanner.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly.Server": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}