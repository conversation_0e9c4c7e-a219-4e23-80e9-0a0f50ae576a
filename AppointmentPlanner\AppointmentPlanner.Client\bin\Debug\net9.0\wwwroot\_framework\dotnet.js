//! Licensed to the .NET Foundation under one or more agreements.
//! The .NET Foundation licenses this file to you under the MIT license.
var e=!1;const t=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,8,1,6,0,6,64,25,11,11])),o=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])),n=Symbol.for("wasm promise_control");function r(e,t){let o=null;const r=new Promise((function(n,r){o={isDone:!1,promise:null,resolve:t=>{o.isDone||(o.isDone=!0,n(t),e&&e())},reject:e=>{o.isDone||(o.isDone=!0,r(e),t&&t())}}}));o.promise=r;const i=r;return i[n]=o,{promise:i,promise_control:o}}function i(e){return e[n]}function s(e){e&&function(e){return void 0!==e[n]}(e)||Ke(!1,"Promise is not controllable")}const a="__mono_message__",l=["debug","log","trace","warn","info","error"],c="MONO_WASM: ";let u,d,f,m;function g(e){m=e}function h(e){if(qe.diagnosticTracing){const t="function"==typeof e?e():e;console.debug(c+t)}}function p(e,...t){console.info(c+e,...t)}function b(e,...t){console.info(e,...t)}function w(e,...t){console.warn(c+e,...t)}function y(e,...t){if(t&&t.length>0&&t[0]&&"object"==typeof t[0]){if(t[0].silent)return;if(t[0].toString)return void console.error(c+e,t[0].toString())}console.error(c+e,...t)}function v(e,t,o){return function(...n){try{let r=n[0];if(void 0===r)r="undefined";else if(null===r)r="null";else if("function"==typeof r)r=r.toString();else if("string"!=typeof r)try{r=JSON.stringify(r)}catch(e){r=r.toString()}t(o?JSON.stringify({method:e,payload:r,arguments:n.slice(1)}):[e+r,...n.slice(1)])}catch(e){f.error(`proxyConsole failed: ${e}`)}}}function _(e,t,o){d=t,m=e,f={...t};const n=`${o}/console`.replace("https://","wss://").replace("http://","ws://");u=new WebSocket(n),u.addEventListener("error",R),u.addEventListener("close",j),function(){for(const e of l)d[e]=v(`console.${e}`,T,!0)}()}function E(e){let t=30;const o=()=>{u?0==u.bufferedAmount||0==t?(e&&b(e),function(){for(const e of l)d[e]=v(`console.${e}`,f.log,!1)}(),u.removeEventListener("error",R),u.removeEventListener("close",j),u.close(1e3,e),u=void 0):(t--,globalThis.setTimeout(o,100)):e&&f&&f.log(e)};o()}function T(e){u&&u.readyState===WebSocket.OPEN?u.send(e):f.log(e)}function R(e){f.error(`[${m}] proxy console websocket error: ${e}`,e)}function j(e){f.debug(`[${m}] proxy console websocket closed: ${e}`,e)}(new Date).valueOf();const x={},A={},S={};let O,D,k;function C(){const e=Object.values(S),t=Object.values(A),o=L(e),n=L(t),r=o+n;if(0===r)return;const i=We?"%c":"",s=We?["background: purple; color: white; padding: 1px 3px; border-radius: 3px;","font-weight: bold;","font-weight: normal;"]:[],a=qe.config.linkerEnabled?"":"\nThis application was built with linking (tree shaking) disabled. \nPublished applications will be significantly smaller if you install wasm-tools workload. \nSee also https://aka.ms/dotnet-wasm-features";console.groupCollapsed(`${i}dotnet${i} Loaded ${U(r)} resources${i}${a}`,...s),e.length&&(console.groupCollapsed(`Loaded ${U(o)} resources from cache`),console.table(S),console.groupEnd()),t.length&&(console.groupCollapsed(`Loaded ${U(n)} resources from network`),console.table(A),console.groupEnd()),console.groupEnd()}async function I(){const e=O;if(e){const t=(await e.keys()).map((async t=>{t.url in x||await e.delete(t)}));await Promise.all(t)}}function M(e){return`${e.resolvedUrl}.${e.hash}`}async function P(){O=await async function(e){if(!qe.config.cacheBootResources||void 0===globalThis.caches||void 0===globalThis.document)return null;if(!1===globalThis.isSecureContext)return null;const t=`dotnet-resources-${globalThis.document.baseURI.substring(globalThis.document.location.origin.length)}`;try{return await caches.open(t)||null}catch(e){return null}}()}function L(e){return e.reduce(((e,t)=>e+(t.responseBytes||0)),0)}function U(e){return`${(e/1048576).toFixed(2)} MB`}function $(){qe.preferredIcuAsset=N(qe.config);let e="invariant"==qe.config.globalizationMode;if(!e)if(qe.preferredIcuAsset)qe.diagnosticTracing&&h("ICU data archive(s) available, disabling invariant mode");else{if("custom"===qe.config.globalizationMode||"all"===qe.config.globalizationMode||"sharded"===qe.config.globalizationMode){const e="invariant globalization mode is inactive and no ICU data archives are available";throw y(`ERROR: ${e}`),new Error(e)}qe.diagnosticTracing&&h("ICU data archive(s) not available, using invariant globalization mode"),e=!0,qe.preferredIcuAsset=null}const t="DOTNET_SYSTEM_GLOBALIZATION_INVARIANT",o="DOTNET_SYSTEM_GLOBALIZATION_HYBRID",n=qe.config.environmentVariables;if(void 0===n[o]&&"hybrid"===qe.config.globalizationMode?n[o]="1":void 0===n[t]&&e&&(n[t]="1"),void 0===n.TZ)try{const e=Intl.DateTimeFormat().resolvedOptions().timeZone||null;e&&(n.TZ=e)}catch(e){p("failed to detect timezone, will fallback to UTC")}}function N(e){var t;if((null===(t=e.resources)||void 0===t?void 0:t.icu)&&"invariant"!=e.globalizationMode){const t=e.applicationCulture||(We?globalThis.navigator&&globalThis.navigator.languages&&globalThis.navigator.languages[0]:Intl.DateTimeFormat().resolvedOptions().locale),o=Object.keys(e.resources.icu),n={};for(let t=0;t<o.length;t++){const r=o[t];e.resources.fingerprinting?n[me(r)]=r:n[r]=r}let r=null;if("custom"===e.globalizationMode){if(o.length>=1)return o[0]}else"hybrid"===e.globalizationMode?r="icudt_hybrid.dat":t&&"all"!==e.globalizationMode?"sharded"===e.globalizationMode&&(r=function(e){const t=e.split("-")[0];return"en"===t||["fr","fr-FR","it","it-IT","de","de-DE","es","es-ES"].includes(e)?"icudt_EFIGS.dat":["zh","ko","ja"].includes(t)?"icudt_CJK.dat":"icudt_no_CJK.dat"}(t)):r="icudt.dat";if(r&&n[r])return n[r]}return e.globalizationMode="invariant",null}const z=class{constructor(e){this.url=e}toString(){return this.url}};async function W(e,t){try{const o="function"==typeof globalThis.fetch;if(Ue){const n=e.startsWith("file://");if(!n&&o)return globalThis.fetch(e,t||{credentials:"same-origin"});D||(k=He.require("url"),D=He.require("fs")),n&&(e=k.fileURLToPath(e));const r=await D.promises.readFile(e);return{ok:!0,headers:{length:0,get:()=>null},url:e,arrayBuffer:()=>r,json:()=>JSON.parse(r),text:()=>{throw new Error("NotImplementedException")}}}if(o)return globalThis.fetch(e,t||{credentials:"same-origin"});if("function"==typeof read)return{ok:!0,url:e,headers:{length:0,get:()=>null},arrayBuffer:()=>new Uint8Array(read(e,"binary")),json:()=>JSON.parse(read(e,"utf8")),text:()=>read(e,"utf8")}}catch(t){return{ok:!1,url:e,status:500,headers:{length:0,get:()=>null},statusText:"ERR28: "+t,arrayBuffer:()=>{throw t},json:()=>{throw t},text:()=>{throw t}}}throw new Error("No fetch implementation available")}function B(e){return"string"!=typeof e&&Ke(!1,"url must be a string"),!q(e)&&0!==e.indexOf("./")&&0!==e.indexOf("../")&&globalThis.URL&&globalThis.document&&globalThis.document.baseURI&&(e=new URL(e,globalThis.document.baseURI).toString()),e}const F=/^[a-zA-Z][a-zA-Z\d+\-.]*?:\/\//,V=/[a-zA-Z]:[\\/]/;function q(e){return Ue||Be?e.startsWith("/")||e.startsWith("\\")||-1!==e.indexOf("///")||V.test(e):F.test(e)}let G,H=0;const J=[],Z=[],Q=new Map,Y={"js-module-threads":!0,"js-module-globalization":!0,"js-module-runtime":!0,"js-module-dotnet":!0,"js-module-native":!0},K={...Y,"js-module-library-initializer":!0},X={...Y,dotnetwasm:!0,heap:!0,manifest:!0},ee={...K,manifest:!0},te={...K,dotnetwasm:!0},oe={dotnetwasm:!0,symbols:!0,"segmentation-rules":!0},ne={...K,dotnetwasm:!0,symbols:!0,"segmentation-rules":!0},re={symbols:!0,"segmentation-rules":!0};function ie(e){return!("icu"==e.behavior&&e.name!=qe.preferredIcuAsset)}function se(e,t,o){const n=Object.keys(t||{});Ke(1==n.length,`Expect to have one ${o} asset in resources`);const r=n[0],i={name:r,hash:t[r],behavior:o};return ae(i),e.push(i),i}function ae(e){X[e.behavior]&&Q.set(e.behavior,e)}function le(e){const t=function(e){Ke(X[e],`Unknown single asset behavior ${e}`);const t=Q.get(e);return Ke(t,`Single asset for ${e} not found`),t}(e);if(!t.resolvedUrl)if(t.resolvedUrl=qe.locateFile(t.name),Y[t.behavior]){const e=Te(t);e?("string"!=typeof e&&Ke(!1,"loadBootResource response for 'dotnetjs' type should be a URL string"),t.resolvedUrl=e):t.resolvedUrl=we(t.resolvedUrl,t.behavior)}else if("dotnetwasm"!==t.behavior)throw new Error(`Unknown single asset behavior ${e}`);return t}let ce=!1;async function ue(){if(!ce){ce=!0,qe.diagnosticTracing&&h("mono_download_assets");try{const e=[],t=[],o=(e,t)=>{!ne[e.behavior]&&ie(e)&&qe.expected_instantiated_assets_count++,!te[e.behavior]&&ie(e)&&(qe.expected_downloaded_assets_count++,t.push(he(e)))};for(const t of J)o(t,e);for(const e of Z)o(e,t);qe.allDownloadsQueued.promise_control.resolve(),Promise.all([...e,...t]).then((()=>{qe.allDownloadsFinished.promise_control.resolve()})).catch((e=>{throw qe.err("Error in mono_download_assets: "+e),at(1,e),e})),await qe.runtimeModuleLoaded.promise;const n=async e=>{const t=await e;if(t.buffer){if(!ne[t.behavior]){t.buffer&&"object"==typeof t.buffer||Ke(!1,"asset buffer must be array-like or buffer-like or promise of these"),"string"!=typeof t.resolvedUrl&&Ke(!1,"resolvedUrl must be string");const e=t.resolvedUrl,o=await t.buffer,n=new Uint8Array(o);Re(t),await Fe.beforeOnRuntimeInitialized.promise,Fe.instantiate_asset(t,e,n)}}else oe[t.behavior]?("symbols"===t.behavior?(await Fe.instantiate_symbols_asset(t),Re(t)):"segmentation-rules"===t.behavior&&(await Fe.instantiate_segmentation_rules_asset(t),Re(t)),oe[t.behavior]&&++qe.actual_downloaded_assets_count):(t.isOptional||Ke(!1,"Expected asset to have the downloaded buffer"),!te[t.behavior]&&ie(t)&&qe.expected_downloaded_assets_count--,!ne[t.behavior]&&ie(t)&&qe.expected_instantiated_assets_count--)},r=[],i=[];for(const t of e)r.push(n(t));for(const e of t)i.push(n(e));Promise.all(r).then((()=>{ze||Fe.coreAssetsInMemory.promise_control.resolve()})).catch((e=>{throw qe.err("Error in mono_download_assets: "+e),at(1,e),e})),Promise.all(i).then((async()=>{ze||(await Fe.coreAssetsInMemory.promise,Fe.allAssetsInMemory.promise_control.resolve())})).catch((e=>{throw qe.err("Error in mono_download_assets: "+e),at(1,e),e}))}catch(e){throw qe.err("Error in mono_download_assets: "+e),e}}}let de=!1;function fe(){if(de)return;de=!0;const e=qe.config,t=[];if(e.assets)for(const t of e.assets)"object"!=typeof t&&Ke(!1,`asset must be object, it was ${typeof t} : ${t}`),"string"!=typeof t.behavior&&Ke(!1,"asset behavior must be known string"),"string"!=typeof t.name&&Ke(!1,"asset name must be string"),t.resolvedUrl&&"string"!=typeof t.resolvedUrl&&Ke(!1,"asset resolvedUrl could be string"),t.hash&&"string"!=typeof t.hash&&Ke(!1,"asset resolvedUrl could be string"),t.pendingDownload&&"object"!=typeof t.pendingDownload&&Ke(!1,"asset pendingDownload could be object"),t.isCore?J.push(t):Z.push(t),ae(t);else if(e.resources){const o=e.resources;o.wasmNative||Ke(!1,"resources.wasmNative must be defined"),o.jsModuleNative||Ke(!1,"resources.jsModuleNative must be defined"),o.jsModuleRuntime||Ke(!1,"resources.jsModuleRuntime must be defined"),se(Z,o.wasmNative,"dotnetwasm"),se(t,o.jsModuleNative,"js-module-native"),se(t,o.jsModuleRuntime,"js-module-runtime"),"hybrid"==e.globalizationMode&&se(t,o.jsModuleGlobalization,"js-module-globalization");const n=(e,t)=>{!o.fingerprinting||"assembly"!=e.behavior&&"pdb"!=e.behavior&&"resource"!=e.behavior||(e.virtualPath=me(e.name)),t?(e.isCore=!0,J.push(e)):Z.push(e)};if(o.coreAssembly)for(const e in o.coreAssembly)n({name:e,hash:o.coreAssembly[e],behavior:"assembly"},!0);if(o.assembly)for(const e in o.assembly)n({name:e,hash:o.assembly[e],behavior:"assembly"},!o.coreAssembly);if(0!=e.debugLevel){if(o.corePdb)for(const e in o.corePdb)n({name:e,hash:o.corePdb[e],behavior:"pdb"},!0);if(o.pdb)for(const e in o.pdb)n({name:e,hash:o.pdb[e],behavior:"pdb"},!o.corePdb)}if(e.loadAllSatelliteResources&&o.satelliteResources)for(const e in o.satelliteResources)for(const t in o.satelliteResources[e])n({name:t,hash:o.satelliteResources[e][t],behavior:"resource",culture:e},!o.coreAssembly);if(o.coreVfs)for(const e in o.coreVfs)for(const t in o.coreVfs[e])n({name:t,hash:o.coreVfs[e][t],behavior:"vfs",virtualPath:e},!0);if(o.vfs)for(const e in o.vfs)for(const t in o.vfs[e])n({name:t,hash:o.vfs[e][t],behavior:"vfs",virtualPath:e},!o.coreVfs);const r=N(e);if(r&&o.icu)for(const e in o.icu)e===r?Z.push({name:e,hash:o.icu[e],behavior:"icu",loadRemote:!0}):e.startsWith("segmentation-rules")&&e.endsWith(".json")&&Z.push({name:e,hash:o.icu[e],behavior:"segmentation-rules"});if(o.wasmSymbols)for(const e in o.wasmSymbols)J.push({name:e,hash:o.wasmSymbols[e],behavior:"symbols"})}if(e.appsettings)for(let t=0;t<e.appsettings.length;t++){const o=e.appsettings[t],n=je(o);"appsettings.json"!==n&&n!==`appsettings.${e.applicationEnvironment}.json`||Z.push({name:o,behavior:"vfs",noCache:!0,useCredentials:!0})}e.assets=[...J,...Z,...t]}function me(e){var t;const o=null===(t=qe.config.resources)||void 0===t?void 0:t.fingerprinting;return o&&o[e]?o[e]:e}async function ge(e){const t=await he(e);return await t.pendingDownloadInternal.response,t.buffer}async function he(e){try{return await pe(e)}catch(t){if(!qe.enableDownloadRetry)throw t;if(Be||Ue)throw t;if(e.pendingDownload&&e.pendingDownloadInternal==e.pendingDownload)throw t;if(e.resolvedUrl&&-1!=e.resolvedUrl.indexOf("file://"))throw t;if(t&&404==t.status)throw t;e.pendingDownloadInternal=void 0,await qe.allDownloadsQueued.promise;try{return qe.diagnosticTracing&&h(`Retrying download '${e.name}'`),await pe(e)}catch(t){return e.pendingDownloadInternal=void 0,await new Promise((e=>globalThis.setTimeout(e,100))),qe.diagnosticTracing&&h(`Retrying download (2) '${e.name}' after delay`),await pe(e)}}}async function pe(e){for(;G;)await G.promise;try{++H,H==qe.maxParallelDownloads&&(qe.diagnosticTracing&&h("Throttling further parallel downloads"),G=r());const t=await async function(e){if(e.pendingDownload&&(e.pendingDownloadInternal=e.pendingDownload),e.pendingDownloadInternal&&e.pendingDownloadInternal.response)return e.pendingDownloadInternal.response;if(e.buffer){const t=await e.buffer;return e.resolvedUrl||(e.resolvedUrl="undefined://"+e.name),e.pendingDownloadInternal={url:e.resolvedUrl,name:e.name,response:Promise.resolve({ok:!0,arrayBuffer:()=>t,json:()=>JSON.parse(new TextDecoder("utf-8").decode(t)),text:()=>{throw new Error("NotImplementedException")},headers:{get:()=>{}}})},e.pendingDownloadInternal.response}const t=e.loadRemote&&qe.config.remoteSources?qe.config.remoteSources:[""];let o;for(let n of t){n=n.trim(),"./"===n&&(n="");const t=be(e,n);e.name===t?qe.diagnosticTracing&&h(`Attempting to download '${t}'`):qe.diagnosticTracing&&h(`Attempting to download '${t}' for ${e.name}`);try{e.resolvedUrl=t;const n=_e(e);if(e.pendingDownloadInternal=n,o=await n.response,!o||!o.ok)continue;return o}catch(e){o||(o={ok:!1,url:t,status:0,statusText:""+e});continue}}const n=e.isOptional||e.name.match(/\.pdb$/)&&qe.config.ignorePdbLoadErrors;if(o||Ke(!1,`Response undefined ${e.name}`),!n){const t=new Error(`download '${o.url}' for ${e.name} failed ${o.status} ${o.statusText}`);throw t.status=o.status,t}p(`optional download '${o.url}' for ${e.name} failed ${o.status} ${o.statusText}`)}(e);return t?(oe[e.behavior]||(e.buffer=await t.arrayBuffer(),++qe.actual_downloaded_assets_count),e):e}finally{if(--H,G&&H==qe.maxParallelDownloads-1){qe.diagnosticTracing&&h("Resuming more parallel downloads");const e=G;G=void 0,e.promise_control.resolve()}}}function be(e,t){let o;return null==t&&Ke(!1,`sourcePrefix must be provided for ${e.name}`),e.resolvedUrl?o=e.resolvedUrl:(o=""===t?"assembly"===e.behavior||"pdb"===e.behavior?e.name:"resource"===e.behavior&&e.culture&&""!==e.culture?`${e.culture}/${e.name}`:e.name:t+e.name,o=we(qe.locateFile(o),e.behavior)),o&&"string"==typeof o||Ke(!1,"attemptUrl need to be path or url string"),o}function we(e,t){return qe.modulesUniqueQuery&&ee[t]&&(e+=qe.modulesUniqueQuery),e}let ye=0;const ve=new Set;function _e(e){try{e.resolvedUrl||Ke(!1,"Request's resolvedUrl must be set");const t=async function(e){let t=await async function(e){const t=O;if(!t||e.noCache||!e.hash||0===e.hash.length)return;const o=M(e);let n;x[o]=!0;try{n=await t.match(o)}catch(e){}if(!n)return;const r=parseInt(n.headers.get("content-length")||"0");return S[e.name]={responseBytes:r},n}(e);return t||(t=await function(e){let t=e.resolvedUrl;if(qe.loadBootResource){const o=Te(e);if(o instanceof Promise)return o;"string"==typeof o&&(t=o)}const o={};return qe.config.disableNoCacheFetch||(o.cache="no-cache"),e.useCredentials?o.credentials="include":!qe.config.disableIntegrityCheck&&e.hash&&(o.integrity=e.hash),qe.fetch_like(t,o)}(e),function(e,t){const o=O;if(!o||e.noCache||!e.hash||0===e.hash.length)return;const n=t.clone();setTimeout((()=>{const t=M(e);!async function(e,t,o,n){const r=await n.arrayBuffer(),i=function(e){if("undefined"!=typeof performance)return performance.getEntriesByName(e)[0]}(n.url),s=i&&i.encodedBodySize||void 0;A[t]={responseBytes:s};const a=new Response(r,{headers:{"content-type":n.headers.get("content-type")||"","content-length":(s||n.headers.get("content-length")||"").toString()}});try{await e.put(o,a)}catch(e){}}(o,e.name,t,n)}),0)}(e,t)),t}(e),o={name:e.name,url:e.resolvedUrl,response:t};return ve.add(e.name),o.response.then((()=>{"assembly"==e.behavior&&qe.loadedAssemblies.push(e.name),ye++,qe.onDownloadResourceProgress&&qe.onDownloadResourceProgress(ye,ve.size)})),o}catch(t){const o={ok:!1,url:e.resolvedUrl,status:500,statusText:"ERR29: "+t,arrayBuffer:()=>{throw t},json:()=>{throw t}};return{name:e.name,url:e.resolvedUrl,response:Promise.resolve(o)}}}const Ee={resource:"assembly",assembly:"assembly",pdb:"pdb",icu:"globalization",vfs:"configuration",manifest:"manifest",dotnetwasm:"dotnetwasm","js-module-dotnet":"dotnetjs","js-module-native":"dotnetjs","js-module-runtime":"dotnetjs","js-module-threads":"dotnetjs"};function Te(e){var t;if(qe.loadBootResource){const o=null!==(t=e.hash)&&void 0!==t?t:"",n=e.resolvedUrl,r=Ee[e.behavior];if(r){const t=qe.loadBootResource(r,e.name,n,o,e.behavior);return"string"==typeof t?B(t):t}}}function Re(e){e.pendingDownloadInternal=null,e.pendingDownload=null,e.buffer=null,e.moduleExports=null}function je(e){let t=e.lastIndexOf("/");return t>=0&&t++,e.substring(t)}async function xe(e){if(!e)return;const t=Object.keys(e);await Promise.all(t.map((e=>async function(e){try{const t=we(qe.locateFile(e),"js-module-library-initializer");qe.diagnosticTracing&&h(`Attempting to import '${t}' for ${e}`);const o=await import(/*! webpackIgnore: true */t);qe.libraryInitializers.push({scriptName:e,exports:o})}catch(t){w(`Failed to import library initializer '${e}': ${t}`)}}(e))))}async function Ae(e,t){if(!qe.libraryInitializers)return;const o=[];for(let n=0;n<qe.libraryInitializers.length;n++){const r=qe.libraryInitializers[n];r.exports[e]&&o.push(Se(r.scriptName,e,(()=>r.exports[e](...t))))}await Promise.all(o)}async function Se(e,t,o){try{await o()}catch(o){throw w(`Failed to invoke '${t}' on library initializer '${e}': ${o}`),at(1,o),o}}var Oe="Release";function De(e,t){if(e===t)return e;const o={...t};return void 0!==o.assets&&o.assets!==e.assets&&(o.assets=[...e.assets||[],...o.assets||[]]),void 0!==o.resources&&(o.resources=Ce(e.resources||{assembly:{},jsModuleNative:{},jsModuleRuntime:{},wasmNative:{}},o.resources)),void 0!==o.environmentVariables&&(o.environmentVariables={...e.environmentVariables||{},...o.environmentVariables||{}}),void 0!==o.runtimeOptions&&o.runtimeOptions!==e.runtimeOptions&&(o.runtimeOptions=[...e.runtimeOptions||[],...o.runtimeOptions||[]]),Object.assign(e,o)}function ke(e,t){if(e===t)return e;const o={...t};return o.config&&(e.config||(e.config={}),o.config=De(e.config,o.config)),Object.assign(e,o)}function Ce(e,t){if(e===t)return e;const o={...t};return void 0!==o.assembly&&(o.assembly={...e.assembly||{},...o.assembly||{}}),void 0!==o.lazyAssembly&&(o.lazyAssembly={...e.lazyAssembly||{},...o.lazyAssembly||{}}),void 0!==o.pdb&&(o.pdb={...e.pdb||{},...o.pdb||{}}),void 0!==o.jsModuleWorker&&(o.jsModuleWorker={...e.jsModuleWorker||{},...o.jsModuleWorker||{}}),void 0!==o.jsModuleNative&&(o.jsModuleNative={...e.jsModuleNative||{},...o.jsModuleNative||{}}),void 0!==o.jsModuleGlobalization&&(o.jsModuleGlobalization={...e.jsModuleGlobalization||{},...o.jsModuleGlobalization||{}}),void 0!==o.jsModuleRuntime&&(o.jsModuleRuntime={...e.jsModuleRuntime||{},...o.jsModuleRuntime||{}}),void 0!==o.wasmSymbols&&(o.wasmSymbols={...e.wasmSymbols||{},...o.wasmSymbols||{}}),void 0!==o.wasmNative&&(o.wasmNative={...e.wasmNative||{},...o.wasmNative||{}}),void 0!==o.icu&&(o.icu={...e.icu||{},...o.icu||{}}),void 0!==o.satelliteResources&&(o.satelliteResources=Ie(e.satelliteResources||{},o.satelliteResources||{})),void 0!==o.modulesAfterConfigLoaded&&(o.modulesAfterConfigLoaded={...e.modulesAfterConfigLoaded||{},...o.modulesAfterConfigLoaded||{}}),void 0!==o.modulesAfterRuntimeReady&&(o.modulesAfterRuntimeReady={...e.modulesAfterRuntimeReady||{},...o.modulesAfterRuntimeReady||{}}),void 0!==o.extensions&&(o.extensions={...e.extensions||{},...o.extensions||{}}),void 0!==o.vfs&&(o.vfs=Ie(e.vfs||{},o.vfs||{})),Object.assign(e,o)}function Ie(e,t){if(e===t)return e;for(const o in t)e[o]={...e[o],...t[o]};return e}function Me(){const e=qe.config;if(e.environmentVariables=e.environmentVariables||{},e.runtimeOptions=e.runtimeOptions||[],e.resources=e.resources||{assembly:{},jsModuleNative:{},jsModuleGlobalization:{},jsModuleWorker:{},jsModuleRuntime:{},wasmNative:{},vfs:{},satelliteResources:{}},e.assets){qe.diagnosticTracing&&h("config.assets is deprecated, use config.resources instead");for(const t of e.assets){const o={};o[t.name]=t.hash||"";const n={};switch(t.behavior){case"assembly":n.assembly=o;break;case"pdb":n.pdb=o;break;case"resource":n.satelliteResources={},n.satelliteResources[t.culture]=o;break;case"icu":n.icu=o;break;case"symbols":n.wasmSymbols=o;break;case"vfs":n.vfs={},n.vfs[t.virtualPath]=o;break;case"dotnetwasm":n.wasmNative=o;break;case"js-module-threads":n.jsModuleWorker=o;break;case"js-module-globalization":n.jsModuleGlobalization=o;break;case"js-module-runtime":n.jsModuleRuntime=o;break;case"js-module-native":n.jsModuleNative=o;break;case"js-module-dotnet":break;default:throw new Error(`Unexpected behavior ${t.behavior} of asset ${t.name}`)}Ce(e.resources,n)}}void 0===e.debugLevel&&"Debug"===Oe&&(e.debugLevel=-1),void 0===e.cachedResourcesPurgeDelay&&(e.cachedResourcesPurgeDelay=1e4),e.applicationCulture&&(e.environmentVariables.LANG=`${e.applicationCulture}.UTF-8`),Fe.diagnosticTracing=qe.diagnosticTracing=!!e.diagnosticTracing,Fe.waitForDebugger=e.waitForDebugger,Fe.enablePerfMeasure=!!e.browserProfilerOptions&&globalThis.performance&&"function"==typeof globalThis.performance.measure,qe.maxParallelDownloads=e.maxParallelDownloads||qe.maxParallelDownloads,qe.enableDownloadRetry=void 0!==e.enableDownloadRetry?e.enableDownloadRetry:qe.enableDownloadRetry}let Pe=!1;async function Le(e){var t;if(Pe)return void await qe.afterConfigLoaded.promise;let o;try{if(e.configSrc||qe.config&&0!==Object.keys(qe.config).length&&(qe.config.assets||qe.config.resources)||(e.configSrc="./blazor.boot.json"),o=e.configSrc,Pe=!0,o&&(qe.diagnosticTracing&&h("mono_wasm_load_config"),await async function(e){const t=qe.locateFile(e.configSrc),o=void 0!==qe.loadBootResource?qe.loadBootResource("manifest","blazor.boot.json",t,"","manifest"):i(t);let n;n=o?"string"==typeof o?await i(B(o)):await o:await i(we(t,"manifest"));const r=await async function(e){const t=qe.config,o=await e.json();t.applicationEnvironment||(o.applicationEnvironment=e.headers.get("Blazor-Environment")||e.headers.get("DotNet-Environment")||"Production"),o.environmentVariables||(o.environmentVariables={});const n=e.headers.get("DOTNET-MODIFIABLE-ASSEMBLIES");n&&(o.environmentVariables.DOTNET_MODIFIABLE_ASSEMBLIES=n);const r=e.headers.get("ASPNETCORE-BROWSER-TOOLS");return r&&(o.environmentVariables.__ASPNETCORE_BROWSER_TOOLS=r),o}(n);function i(e){return qe.fetch_like(e,{method:"GET",credentials:"include",cache:"no-cache"})}De(qe.config,r)}(e)),Me(),await xe(null===(t=qe.config.resources)||void 0===t?void 0:t.modulesAfterConfigLoaded),await Ae("onRuntimeConfigLoaded",[qe.config]),e.onConfigLoaded)try{await e.onConfigLoaded(qe.config,Ge),Me()}catch(e){throw y("onConfigLoaded() failed",e),e}Me(),qe.afterConfigLoaded.promise_control.resolve(qe.config)}catch(t){const n=`Failed to load config file ${o} ${t} ${null==t?void 0:t.stack}`;throw qe.config=e.config=Object.assign(qe.config,{message:n,error:t,isError:!0}),at(1,new Error(n)),t}}"function"!=typeof importScripts||globalThis.onmessage||(globalThis.dotnetSidecar=!0);const Ue="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,$e="function"==typeof importScripts,Ne=$e&&"undefined"!=typeof dotnetSidecar,ze=$e&&!Ne,We="object"==typeof window||$e&&!Ue,Be=!We&&!Ue;let Fe={},Ve={},qe={},Ge={},He={},Je=!1;const Ze={},Qe={config:Ze},Ye={mono:{},binding:{},internal:He,module:Qe,loaderHelpers:qe,runtimeHelpers:Fe,globalizationHelpers:Ve,api:Ge};function Ke(e,t){if(e)return;const o="Assert failed: "+("function"==typeof t?t():t),n=new Error(o);y(o,n),Fe.nativeAbort(n)}function Xe(){return void 0!==qe.exitCode}function et(){return Fe.runtimeReady&&!Xe()}function tt(){Xe()&&Ke(!1,`.NET runtime already exited with ${qe.exitCode} ${qe.exitReason}. You can use runtime.runMain() which doesn't exit the runtime.`),Fe.runtimeReady||Ke(!1,".NET runtime didn't start yet. Please call dotnet.create() first.")}function ot(){We&&(globalThis.addEventListener("unhandledrejection",ct),globalThis.addEventListener("error",ut))}let nt,rt;function it(e){rt&&rt(e),at(e,qe.exitReason)}function st(e){nt&&nt(e||qe.exitReason),at(1,e||qe.exitReason)}function at(t,o){var n,r;const i=o&&"object"==typeof o;t=i&&"number"==typeof o.status?o.status:void 0===t?-1:t;const s=i&&"string"==typeof o.message?o.message:""+o;(o=i?o:Fe.ExitStatus?function(e,t){const o=new Fe.ExitStatus(e);return o.message=t,o.toString=()=>t,o}(t,s):new Error("Exit with code "+t+" "+s)).status=t,o.message||(o.message=s);const a=""+(o.stack||(new Error).stack);try{Object.defineProperty(o,"stack",{get:()=>a})}catch(e){}const l=!!o.silent;if(o.silent=!0,Xe())qe.diagnosticTracing&&h("mono_exit called after exit");else{try{Qe.onAbort==st&&(Qe.onAbort=nt),Qe.onExit==it&&(Qe.onExit=rt),We&&(globalThis.removeEventListener("unhandledrejection",ct),globalThis.removeEventListener("error",ut)),Fe.runtimeReady?(Fe.jiterpreter_dump_stats&&Fe.jiterpreter_dump_stats(!1),0===t&&(null===(n=qe.config)||void 0===n?void 0:n.interopCleanupOnExit)&&Fe.forceDisposeProxies(!0,!0),e&&0!==t&&(null===(r=qe.config)||void 0===r||r.dumpThreadsOnNonZeroExit)):(qe.diagnosticTracing&&h(`abort_startup, reason: ${o}`),function(e){qe.allDownloadsQueued.promise_control.reject(e),qe.allDownloadsFinished.promise_control.reject(e),qe.afterConfigLoaded.promise_control.reject(e),qe.wasmCompilePromise.promise_control.reject(e),qe.runtimeModuleLoaded.promise_control.reject(e),Fe.dotnetReady&&(Fe.dotnetReady.promise_control.reject(e),Fe.afterInstantiateWasm.promise_control.reject(e),Fe.beforePreInit.promise_control.reject(e),Fe.afterPreInit.promise_control.reject(e),Fe.afterPreRun.promise_control.reject(e),Fe.beforeOnRuntimeInitialized.promise_control.reject(e),Fe.afterOnRuntimeInitialized.promise_control.reject(e),Fe.afterPostRun.promise_control.reject(e))}(o))}catch(e){w("mono_exit A failed",e)}try{l||(function(e,t){if(0!==e&&t){const e=Fe.ExitStatus&&t instanceof Fe.ExitStatus?h:y;"string"==typeof t?e(t):(void 0===t.stack&&(t.stack=(new Error).stack+""),t.message?e(Fe.stringify_as_error_with_stack?Fe.stringify_as_error_with_stack(t.message+"\n"+t.stack):t.message+"\n"+t.stack):e(JSON.stringify(t)))}!ze&&qe.config&&(qe.config.logExitCode?qe.config.forwardConsoleLogsToWS?E("WASM EXIT "+e):b("WASM EXIT "+e):qe.config.forwardConsoleLogsToWS&&E())}(t,o),function(e){if(We&&!ze&&qe.config&&qe.config.appendElementOnExit&&document){const t=document.createElement("label");t.id="tests_done",0!==e&&(t.style.background="red"),t.innerHTML=""+e,document.body.appendChild(t)}}(t))}catch(e){w("mono_exit B failed",e)}qe.exitCode=t,qe.exitReason||(qe.exitReason=o),!ze&&Fe.runtimeReady&&Qe.runtimeKeepalivePop()}if(qe.config&&qe.config.asyncFlushOnExit&&0===t)throw(async()=>{try{await async function(){try{const e=await import(/*! webpackIgnore: true */"process"),t=e=>new Promise(((t,o)=>{e.on("error",o),e.end("","utf8",t)})),o=t(e.stderr),n=t(e.stdout);let r;const i=new Promise((e=>{r=setTimeout((()=>e("timeout")),1e3)}));await Promise.race([Promise.all([n,o]),i]),clearTimeout(r)}catch(e){y(`flushing std* streams failed: ${e}`)}}()}finally{lt(t,o)}})(),o;lt(t,o)}function lt(e,t){if(Fe.runtimeReady&&Fe.nativeExit)try{Fe.nativeExit(e)}catch(e){!Fe.ExitStatus||e instanceof Fe.ExitStatus||w("set_exit_code_and_quit_now failed: "+e.toString())}if(0!==e||!We)throw Ue&&He.process?He.process.exit(e):Fe.quit&&Fe.quit(e,t),t}function ct(e){dt(e,e.reason,"rejection")}function ut(e){dt(e,e.error,"error")}function dt(e,t,o){e.preventDefault();try{t||(t=new Error("Unhandled "+o)),void 0===t.stack&&(t.stack=(new Error).stack),t.stack=t.stack+"",t.silent||(y("Unhandled error:",t),at(1,t))}catch(e){}}!function(e){if(Je)throw new Error("Loader module already loaded");Je=!0,Fe=e.runtimeHelpers,Ve=e.globalizationHelpers,qe=e.loaderHelpers,Ge=e.api,He=e.internal,Object.assign(Ge,{INTERNAL:He,invokeLibraryInitializers:Ae}),Object.assign(e.module,{config:De(Ze,{environmentVariables:{}})});const n={mono_wasm_bindings_is_ready:!1,config:e.module.config,diagnosticTracing:!1,nativeAbort:e=>{throw e||new Error("abort")},nativeExit:e=>{throw new Error("exit:"+e)}},a={gitHash:"e36e4d1a8f8dfb08d7e3a6041459c9791d732c01",config:e.module.config,diagnosticTracing:!1,maxParallelDownloads:16,enableDownloadRetry:!0,_loaded_files:[],loadedFiles:[],loadedAssemblies:[],libraryInitializers:[],workerNextNumber:1,actual_downloaded_assets_count:0,actual_instantiated_assets_count:0,expected_downloaded_assets_count:0,expected_instantiated_assets_count:0,afterConfigLoaded:r(),allDownloadsQueued:r(),allDownloadsFinished:r(),wasmCompilePromise:r(),runtimeModuleLoaded:r(),loadingWorkers:r(),is_exited:Xe,is_runtime_running:et,assert_runtime_running:tt,mono_exit:at,createPromiseController:r,getPromiseController:i,assertIsControllablePromise:s,mono_download_assets:ue,resolve_single_asset_path:le,setup_proxy_console:_,set_thread_prefix:g,logDownloadStatsToConsole:C,purgeUnusedCacheEntriesAsync:I,installUnhandledErrorHandler:ot,retrieve_asset_download:ge,invokeLibraryInitializers:Ae,exceptions:t,simd:o};Object.assign(Fe,n),Object.assign(qe,a)}(Ye);let ft,mt,gt=!1,ht=!1;async function pt(e){if(!ht){if(ht=!0,We&&qe.config.forwardConsoleLogsToWS&&void 0!==globalThis.WebSocket&&_("main",globalThis.console,globalThis.location.origin),Qe||Ke(!1,"Null moduleConfig"),qe.config||Ke(!1,"Null moduleConfig.config"),"function"==typeof e){const t=e(Ye.api);if(t.ready)throw new Error("Module.ready couldn't be redefined.");Object.assign(Qe,t),ke(Qe,t)}else{if("object"!=typeof e)throw new Error("Can't use moduleFactory callback of createDotnetRuntime function.");ke(Qe,e)}await async function(e){if(Ue){const e=await import(/*! webpackIgnore: true */"process"),t=14;if(e.versions.node.split(".")[0]<t)throw new Error(`NodeJS at '${e.execPath}' has too low version '${e.versions.node}', please use at least ${t}. See also https://aka.ms/dotnet-wasm-features`)}const t=/*! webpackIgnore: true */import.meta.url,o=t.indexOf("?");var n;if(o>0&&(qe.modulesUniqueQuery=t.substring(o)),qe.scriptUrl=t.replace(/\\/g,"/").replace(/[?#].*/,""),qe.scriptDirectory=(n=qe.scriptUrl).slice(0,n.lastIndexOf("/"))+"/",qe.locateFile=e=>"URL"in globalThis&&globalThis.URL!==z?new URL(e,qe.scriptDirectory).toString():q(e)?e:qe.scriptDirectory+e,qe.fetch_like=W,qe.out=console.log,qe.err=console.error,qe.onDownloadResourceProgress=e.onDownloadResourceProgress,We&&globalThis.navigator){const e=globalThis.navigator,t=e.userAgentData&&e.userAgentData.brands;t&&t.length>0?qe.isChromium=t.some((e=>"Google Chrome"===e.brand||"Microsoft Edge"===e.brand||"Chromium"===e.brand)):e.userAgent&&(qe.isChromium=e.userAgent.includes("Chrome"),qe.isFirefox=e.userAgent.includes("Firefox"))}He.require=Ue?await import(/*! webpackIgnore: true */"module").then((e=>e.createRequire(/*! webpackIgnore: true */import.meta.url))):Promise.resolve((()=>{throw new Error("require not supported")})),void 0===globalThis.URL&&(globalThis.URL=z)}(Qe)}}async function bt(e){return await pt(e),nt=Qe.onAbort,rt=Qe.onExit,Qe.onAbort=st,Qe.onExit=it,Qe.ENVIRONMENT_IS_PTHREAD?async function(){(function(){const e=new MessageChannel,t=e.port1,o=e.port2;t.addEventListener("message",(e=>{var n,r;n=JSON.parse(e.data.config),r=JSON.parse(e.data.monoThreadInfo),gt?qe.diagnosticTracing&&h("mono config already received"):(De(qe.config,n),Fe.monoThreadInfo=r,Me(),qe.diagnosticTracing&&h("mono config received"),gt=!0,qe.afterConfigLoaded.promise_control.resolve(qe.config),We&&n.forwardConsoleLogsToWS&&void 0!==globalThis.WebSocket&&qe.setup_proxy_console("worker-idle",console,globalThis.location.origin)),t.close(),o.close()}),{once:!0}),t.start(),self.postMessage({[a]:{monoCmd:"preload",port:o}},[o])})(),await qe.afterConfigLoaded.promise,function(){const e=qe.config;e.assets||Ke(!1,"config.assets must be defined");for(const t of e.assets)ae(t),re[t.behavior]&&Z.push(t)}(),setTimeout((async()=>{try{await ue()}catch(e){at(1,e)}}),0);const e=wt(),t=await Promise.all(e);return await yt(t),Qe}():async function(){var e;await Le(Qe),fe();const t=wt();await P(),async function(){try{const e=le("dotnetwasm");await he(e),e&&e.pendingDownloadInternal&&e.pendingDownloadInternal.response||Ke(!1,"Can't load dotnet.native.wasm");const t=await e.pendingDownloadInternal.response,o=t.headers&&t.headers.get?t.headers.get("Content-Type"):void 0;let n;if("function"==typeof WebAssembly.compileStreaming&&"application/wasm"===o)n=await WebAssembly.compileStreaming(t);else{We&&"application/wasm"!==o&&w('WebAssembly resource does not have the expected content type "application/wasm", so falling back to slower ArrayBuffer instantiation.');const e=await t.arrayBuffer();qe.diagnosticTracing&&h("instantiate_wasm_module buffered"),n=Be?await Promise.resolve(new WebAssembly.Module(e)):await WebAssembly.compile(e)}e.pendingDownloadInternal=null,e.pendingDownload=null,e.buffer=null,e.moduleExports=null,qe.wasmCompilePromise.promise_control.resolve(n)}catch(e){qe.wasmCompilePromise.promise_control.reject(e)}}(),setTimeout((async()=>{try{$(),await ue()}catch(e){at(1,e)}}),0);const o=await Promise.all(t);return await yt(o),await Fe.dotnetReady.promise,await xe(null===(e=qe.config.resources)||void 0===e?void 0:e.modulesAfterRuntimeReady),await Ae("onRuntimeReady",[Ye.api]),Ge}()}function wt(){const e=le("js-module-runtime"),t=le("js-module-native");return ft&&mt||("object"==typeof e.moduleExports?ft=e.moduleExports:(qe.diagnosticTracing&&h(`Attempting to import '${e.resolvedUrl}' for ${e.name}`),ft=import(/*! webpackIgnore: true */e.resolvedUrl)),"object"==typeof t.moduleExports?mt=t.moduleExports:(qe.diagnosticTracing&&h(`Attempting to import '${t.resolvedUrl}' for ${t.name}`),mt=import(/*! webpackIgnore: true */t.resolvedUrl))),[ft,mt]}async function yt(e){const{initializeExports:t,initializeReplacements:o,configureRuntimeStartup:n,configureEmscriptenStartup:r,configureWorkerStartup:i,setRuntimeGlobals:s,passEmscriptenInternals:a}=e[0],{default:l}=e[1];if(s(Ye),t(Ye),"hybrid"===qe.config.globalizationMode){const e=await async function(){let e;const t=le("js-module-globalization");return"object"==typeof t.moduleExports?e=t.moduleExports:(h(`Attempting to import '${t.resolvedUrl}' for ${t.name}`),e=import(/*! webpackIgnore: true */t.resolvedUrl)),await e}(),{initHybrid:t}=e;t(Ve,Fe)}await n(Qe),qe.runtimeModuleLoaded.promise_control.resolve(),l((e=>(Object.assign(Qe,{ready:e.ready,__dotnet_runtime:{initializeReplacements:o,configureEmscriptenStartup:r,configureWorkerStartup:i,passEmscriptenInternals:a}}),Qe))).catch((e=>{if(e.message&&e.message.toLowerCase().includes("out of memory"))throw new Error(".NET runtime has failed to start, because too much memory was requested. Please decrease the memory by adjusting EmccMaximumHeapSize. See also https://aka.ms/dotnet-wasm-features");throw e}))}const vt=new class{withModuleConfig(e){try{return ke(Qe,e),this}catch(e){throw at(1,e),e}}withOnConfigLoaded(e){try{return ke(Qe,{onConfigLoaded:e}),this}catch(e){throw at(1,e),e}}withConsoleForwarding(){try{return De(Ze,{forwardConsoleLogsToWS:!0}),this}catch(e){throw at(1,e),e}}withExitOnUnhandledError(){try{return De(Ze,{exitOnUnhandledError:!0}),ot(),this}catch(e){throw at(1,e),e}}withAsyncFlushOnExit(){try{return De(Ze,{asyncFlushOnExit:!0}),this}catch(e){throw at(1,e),e}}withExitCodeLogging(){try{return De(Ze,{logExitCode:!0}),this}catch(e){throw at(1,e),e}}withElementOnExit(){try{return De(Ze,{appendElementOnExit:!0}),this}catch(e){throw at(1,e),e}}withInteropCleanupOnExit(){try{return De(Ze,{interopCleanupOnExit:!0}),this}catch(e){throw at(1,e),e}}withDumpThreadsOnNonZeroExit(){try{return De(Ze,{dumpThreadsOnNonZeroExit:!0}),this}catch(e){throw at(1,e),e}}withWaitingForDebugger(e){try{return De(Ze,{waitForDebugger:e}),this}catch(e){throw at(1,e),e}}withInterpreterPgo(e,t){try{return De(Ze,{interpreterPgo:e,interpreterPgoSaveDelay:t}),Ze.runtimeOptions?Ze.runtimeOptions.push("--interp-pgo-recording"):Ze.runtimeOptions=["--interp-pgo-recording"],this}catch(e){throw at(1,e),e}}withConfig(e){try{return De(Ze,e),this}catch(e){throw at(1,e),e}}withConfigSrc(e){try{return e&&"string"==typeof e||Ke(!1,"must be file path or URL"),ke(Qe,{configSrc:e}),this}catch(e){throw at(1,e),e}}withVirtualWorkingDirectory(e){try{return e&&"string"==typeof e||Ke(!1,"must be directory path"),De(Ze,{virtualWorkingDirectory:e}),this}catch(e){throw at(1,e),e}}withEnvironmentVariable(e,t){try{const o={};return o[e]=t,De(Ze,{environmentVariables:o}),this}catch(e){throw at(1,e),e}}withEnvironmentVariables(e){try{return e&&"object"==typeof e||Ke(!1,"must be dictionary object"),De(Ze,{environmentVariables:e}),this}catch(e){throw at(1,e),e}}withDiagnosticTracing(e){try{return"boolean"!=typeof e&&Ke(!1,"must be boolean"),De(Ze,{diagnosticTracing:e}),this}catch(e){throw at(1,e),e}}withDebugging(e){try{return null!=e&&"number"==typeof e||Ke(!1,"must be number"),De(Ze,{debugLevel:e}),this}catch(e){throw at(1,e),e}}withApplicationArguments(...e){try{return e&&Array.isArray(e)||Ke(!1,"must be array of strings"),De(Ze,{applicationArguments:e}),this}catch(e){throw at(1,e),e}}withRuntimeOptions(e){try{return e&&Array.isArray(e)||Ke(!1,"must be array of strings"),Ze.runtimeOptions?Ze.runtimeOptions.push(...e):Ze.runtimeOptions=e,this}catch(e){throw at(1,e),e}}withMainAssembly(e){try{return De(Ze,{mainAssemblyName:e}),this}catch(e){throw at(1,e),e}}withApplicationArgumentsFromQuery(){try{if(!globalThis.window)throw new Error("Missing window to the query parameters from");if(void 0===globalThis.URLSearchParams)throw new Error("URLSearchParams is supported");const e=new URLSearchParams(globalThis.window.location.search).getAll("arg");return this.withApplicationArguments(...e)}catch(e){throw at(1,e),e}}withApplicationEnvironment(e){try{return De(Ze,{applicationEnvironment:e}),this}catch(e){throw at(1,e),e}}withApplicationCulture(e){try{return De(Ze,{applicationCulture:e}),this}catch(e){throw at(1,e),e}}withResourceLoader(e){try{return qe.loadBootResource=e,this}catch(e){throw at(1,e),e}}async download(){try{await async function(){pt(Qe),await Le(Qe),fe(),await P(),$(),ue(),await qe.allDownloadsFinished.promise}()}catch(e){throw at(1,e),e}}async create(){try{return this.instance||(this.instance=await async function(){return await bt(Qe),Ye.api}()),this.instance}catch(e){throw at(1,e),e}}async run(){try{return Qe.config||Ke(!1,"Null moduleConfig.config"),this.instance||await this.create(),this.instance.runMainAndExit()}catch(e){throw at(1,e),e}}},_t=at,Et=bt;Be||"function"==typeof globalThis.URL||Ke(!1,"This browser/engine doesn't support URL API. Please use a modern version. See also https://aka.ms/dotnet-wasm-features"),"function"!=typeof globalThis.BigInt64Array&&Ke(!1,"This browser/engine doesn't support BigInt64Array API. Please use a modern version. See also https://aka.ms/dotnet-wasm-features");export{Et as default,vt as dotnet,_t as exit};
//# sourceMappingURL=dotnet.js.map
