using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using AppointmentPlanner.Data.Models;
using AppointmentPlanner.Models;

namespace AppointmentPlanner.Services
{
    public interface IAuthService
    {
        Task<AuthResult> RegisterAsync(RegisterModel model);
        Task<AuthResult> LoginAsync(LoginModel model);
        Task<bool> LogoutAsync();
        Task<AuthResult> ResetPasswordAsync(ResetPasswordModel model);
        Task<bool> SendPasswordResetEmailAsync(string email);
        Task<ApplicationUser?> GetCurrentUserAsync();
        Task<bool> IsInRoleAsync(ApplicationUser user, string role);
    }

    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            RoleManager<IdentityRole> roleManager,
            IConfiguration configuration,
            ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<AuthResult> RegisterAsync(RegisterModel model)
        {
            try
            {
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null)
                {
                    return new AuthResult { Success = false, Message = "User with this email already exists." };
                }

                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    PhoneNumber = model.PhoneNumber,
                    DateOfBirth = model.DateOfBirth,
                    Gender = model.Gender,
                    Address = model.Address,
                    EmailConfirmed = true // For demo purposes
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (!result.Succeeded)
                {
                    return new AuthResult 
                    { 
                        Success = false, 
                        Message = string.Join(", ", result.Errors.Select(e => e.Description)) 
                    };
                }

                // Assign role
                var roleResult = await _userManager.AddToRoleAsync(user, model.Role);
                if (!roleResult.Succeeded)
                {
                    _logger.LogWarning($"Failed to assign role {model.Role} to user {user.Email}");
                }

                // Create doctor profile if role is Professional
                if (model.Role == "Professional" && model.DoctorProfile != null)
                {
                    // This would be handled by a separate service in a real application
                    _logger.LogInformation($"Doctor profile creation needed for user {user.Email}");
                }

                _logger.LogInformation($"User {user.Email} registered successfully with role {model.Role}");
                return new AuthResult { Success = true, Message = "Registration successful." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration");
                return new AuthResult { Success = false, Message = "An error occurred during registration." };
            }
        }

        public async Task<AuthResult> LoginAsync(LoginModel model)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(model.Email);
                if (user == null || !user.IsActive)
                {
                    return new AuthResult { Success = false, Message = "Invalid email or password." };
                }

                var result = await _signInManager.PasswordSignInAsync(user, model.Password, model.RememberMe, lockoutOnFailure: true);
                
                if (result.Succeeded)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    var token = GenerateJwtToken(user, roles);
                    
                    _logger.LogInformation($"User {user.Email} logged in successfully");
                    return new AuthResult
                    {
                        Success = true,
                        Message = "Login successful.",
                        Token = token,
                        User = new UserInfo
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email!,
                            PhoneNumber = user.PhoneNumber,
                            DateOfBirth = user.DateOfBirth,
                            Gender = user.Gender,
                            Address = user.Address,
                            BloodGroup = user.BloodGroup,
                            IsActive = user.IsActive,
                            CreatedAt = user.CreatedAt,
                            Roles = roles.ToList()
                        },
                        Roles = roles.ToList()
                    };
                }

                if (result.IsLockedOut)
                {
                    return new AuthResult { Success = false, Message = "Account is locked out." };
                }

                return new AuthResult { Success = false, Message = "Invalid email or password." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user login");
                return new AuthResult { Success = false, Message = "An error occurred during login." };
            }
        }

        public async Task<bool> LogoutAsync()
        {
            try
            {
                await _signInManager.SignOutAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return false;
            }
        }

        public async Task<AuthResult> ResetPasswordAsync(ResetPasswordModel model)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(model.Email);
                if (user == null)
                {
                    return new AuthResult { Success = false, Message = "User not found." };
                }

                var result = await _userManager.ResetPasswordAsync(user, model.Token, model.NewPassword);
                if (result.Succeeded)
                {
                    return new AuthResult { Success = true, Message = "Password reset successful." };
                }

                return new AuthResult 
                { 
                    Success = false, 
                    Message = string.Join(", ", result.Errors.Select(e => e.Description)) 
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset");
                return new AuthResult { Success = false, Message = "An error occurred during password reset." };
            }
        }

        public async Task<bool> SendPasswordResetEmailAsync(string email)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    return false; // Don't reveal that user doesn't exist
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                // In a real application, you would send an email with the reset link
                _logger.LogInformation($"Password reset token generated for {email}: {token}");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending password reset email");
                return false;
            }
        }

        public async Task<ApplicationUser?> GetCurrentUserAsync()
        {
            try
            {
                return await _userManager.GetUserAsync(_signInManager.Context.User);
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> IsInRoleAsync(ApplicationUser user, string role)
        {
            return await _userManager.IsInRoleAsync(user, role);
        }

        private string GenerateJwtToken(ApplicationUser user, IList<string> roles)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"];
            var issuer = jwtSettings["Issuer"];
            var audience = jwtSettings["Audience"];
            var expirationMinutes = int.Parse(jwtSettings["ExpirationInMinutes"] ?? "60");

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey!));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Name, user.UserName!),
                new(ClaimTypes.Email, user.Email!),
                new("FirstName", user.FirstName),
                new("LastName", user.LastName)
            };

            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(expirationMinutes),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
