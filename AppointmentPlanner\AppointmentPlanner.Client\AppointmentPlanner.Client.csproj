<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.6" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
		<PackageReference Include="Syncfusion.Blazor.Buttons" Version="26.1.35" />
		<PackageReference Include="Syncfusion.Blazor.Charts" Version="26.1.35" />
		<PackageReference Include="Syncfusion.Blazor.Grid" Version="26.1.35" />
		<PackageReference Include="Syncfusion.Blazor.Navigations" Version="26.1.35" />
		<PackageReference Include="Syncfusion.Blazor.Notifications" Version="26.1.35" />
		<PackageReference Include="Syncfusion.Blazor.Schedule" Version="26.1.35" />
		<PackageReference Include="Syncfusion.Blazor.Themes" Version="26.1.35" />
		<PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

</Project>
