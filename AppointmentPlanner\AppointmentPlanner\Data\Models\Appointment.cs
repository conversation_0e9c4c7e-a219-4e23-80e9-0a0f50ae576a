using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AppointmentPlanner.Data.Models
{
    public class Appointment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public DateTime StartTime { get; set; }

        [Required]
        public DateTime EndTime { get; set; }

        [Required]
        public string PatientId { get; set; } = string.Empty;

        [Required]
        public string DoctorId { get; set; } = string.Empty;

        [Required]
        public int DepartmentId { get; set; }

        [StringLength(500)]
        public string? Disease { get; set; }

        [StringLength(1000)]
        public string? Symptoms { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        public bool IsAllDay { get; set; } = false;

        public bool IsBlock { get; set; } = false;

        [StringLength(500)]
        public string? RecurrenceRule { get; set; }

        public int? RecurrenceId { get; set; }

        [StringLength(500)]
        public string? RecurrenceException { get; set; }

        [StringLength(50)]
        public string? StartTimezone { get; set; }

        [StringLength(50)]
        public string? EndTimezone { get; set; }

        public AppointmentStatus Status { get; set; } = AppointmentStatus.Scheduled;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("PatientId")]
        public virtual ApplicationUser Patient { get; set; } = null!;

        [ForeignKey("DoctorId")]
        public virtual ApplicationUser Doctor { get; set; } = null!;

        public virtual Department Department { get; set; } = null!;
    }

    public enum AppointmentStatus
    {
        Scheduled,
        Confirmed,
        InProgress,
        Completed,
        Cancelled,
        NoShow
    }
}
