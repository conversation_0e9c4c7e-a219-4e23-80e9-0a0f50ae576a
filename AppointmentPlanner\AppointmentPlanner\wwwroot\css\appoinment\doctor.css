﻿.doctors-wrapper {
    margin: 50px;
}

    .doctors-wrapper header {
        display: flex;
    }

        .doctors-wrapper header .module-title {
            margin-left: 15.5px;
            flex: auto;
        }

        .doctors-wrapper header .add-doctor {
            flex: initial;
            display: none;
        }

            .doctors-wrapper header .add-doctor .e-icon-add {
                padding: 9px 0 10px 20px;
                font-size: 10px;
                color: #7575ff;
            }

                .doctors-wrapper header .add-doctor .e-icon-add::before {
                    content: '\e95c';
                }

            .doctors-wrapper header .add-doctor .add-doctor-label {
                font-weight: 600;
                font-size: 13px;
                color: #7575ff;
                letter-spacing: 0.39px;
                padding: 5px;
            }

    .doctors-wrapper .specialization-detail-wrapper {
        /* Specialist list item display */
    }

        .doctors-wrapper .specialization-detail-wrapper .specialization-types {
            display: flex;
            justify-content: flex-end;
        }

            .doctors-wrapper .specialization-detail-wrapper .specialization-types .e-ddl.e-input-group.e-control-wrapper {
                margin: 0 18px;
                width: 200px;
            }

                .doctors-wrapper .specialization-detail-wrapper .specialization-types .e-ddl.e-input-group.e-control-wrapper .specialist-value span:nth-child(1) {
                    margin: 3px 5px;
                }

        .doctors-wrapper .specialization-detail-wrapper .specialist-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-column-gap: 22px;
            grid-row-gap: 29px;
            padding-top: 16px;
        }

            .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item {
                display: flex;
                height: 120px;
                background: #fff;
                box-shadow: 1px 2px 20px 0 rgba(173, 173, 255, 0.20);
            }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item:hover {
                    cursor: pointer;
                    box-shadow: 0px 1px 4px 1px #7575ff;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item:focus, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item:active {
                    border-color: #7575ff;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image {
                    margin: 9.6px 16.5px 10.4px 11.5px;
                    position: relative;
                }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image img.profile {
                        height: 100px;
                        width: 100px;
                        border-radius: 50px;
                    }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .availability {
                        border-radius: 16px;
                        border: 2px solid #fff;
                        height: 20px;
                        width: 20px;
                        margin: 3px;
                        position: absolute;
                        top: 65%;
                        left: 75%;
                    }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .availability.busy {
                            background: #d40000;
                        }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .availability.away {
                            background: #ff8c2e;
                        }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .availability.available {
                            background: #00d406;
                        }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .upload {
                        position: absolute;
                        top: 35%;
                        left: 35%;
                        display: none;
                        font-size: 30px;
                        color: white;
                    }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image:hover .upload.icon-upload_photo.new-doctor {
                        display: block;
                    }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail {
                    margin: 17.5px 0 24.5px;
                }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .name {
                        height: 19px;
                        font-size: 16px;
                        color: #333;
                        letter-spacing: 0.48px;
                        font-weight: 500;
                        max-width: 250px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis
                    }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .education {
                        height: 16px;
                        font-size: 13px;
                        color: #333;
                        letter-spacing: 0.33px;
                        overflow: hidden;
                    }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .education:hover {
                            overflow: visible;
                        }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience {
                        display: flex;
                        margin-top: 6px;
                    }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization {
                            border-right: 1px solid #ababab;
                            padding-right: 12px !important;
                        }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience {
                            padding-left: 12px !important;
                        }

                        .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience {
                            display: flex;
                            flex-direction: column;
                        }

                            .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization .label-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience .label-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization .specialization-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience .specialization-text {
                                font-size: 13px;
                                letter-spacing: 0.39px;
                            }

                            .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization .label-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience .label-text {
                                color: #666;
                                margin-bottom: 6px;
                            }

                            .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization .specialization-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience .specialization-text {
                                color: #333;
                            }



.e-tooltip-wrap {
    border-radius: 15px 15px 15px 0;
    opacity: 0.8;
    background: #333;
}

    .e-tooltip-wrap .e-tip-content {
        border-radius: 15px 15px 15px 0;
        font-size: 13px;
        color: #fff;
        letter-spacing: 0.39px;
        padding: 5px 8px;
    }

.specialist-value {
    display: flex;
    margin: 6px 0;
}

    .specialist-value span:nth-child(1) {
        border-radius: 6px;
        height: 12px;
        width: 12px;
        margin: 5px 3px 0;
    }

#Specialization_popup .e-list-item {
    padding-right: 0;
    text-indent: 5px;
}

    #Specialization_popup .e-list-item .specialist-value {
        margin: 0 5px;
    }

        #Specialization_popup .e-list-item .specialist-value span:nth-child(1) {
            margin: 7px;
        }

.e-dialog.new-doctor-dialog .e-footer-content {
    padding: 15px 15px 0 15px;
}

.e-dialog.new-doctor-dialog {
    max-height: 580px !important;
}

@media (max-width: 850px) {
    .doctors-wrapper {
        margin: 20px 10px;
    }

        .doctors-wrapper header .module-title {
            margin-left: 10px;
        }

        .doctors-wrapper header .add-doctor {
            flex: initial;
            display: flex;
            cursor: pointer;
        }

            .doctors-wrapper header .add-doctor .add-doctor-label {
                margin-right: 6px;
                font-weight: 500;
            }

        .doctors-wrapper .specialization-detail-wrapper .specialization-types {
            display: flex;
            margin: 21px 0 16px 0;
        }

            .doctors-wrapper .specialization-detail-wrapper .specialization-types .e-control-wrapper.e-ddl {
                flex: 1;
                margin: 0;
                width: 100% !important;
            }

            .doctors-wrapper .specialization-detail-wrapper .specialization-types .e-btn {
                display: none;
            }

        .doctors-wrapper .specialization-detail-wrapper .specialist-display {
            grid-template-columns: 1fr;
            grid-gap: 10px;
            padding: 0;
        }

            .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image {
                margin: 29.5px 16.5px 30.5px 11.3px;
            }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .profile {
                    height: 60px !important;
                    width: 60px !important;
                    border-radius: 30px !important;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-image .availability {
                    height: 12px;
                    width: 12px;
                    top: 60%;
                    left: 65%;
                }

            .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail {
                margin: 19px 0 23px;
            }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .name {
                    height: 19px;
                    margin-bottom: 2px;
                    padding: 0;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .education {
                    height: 14px;
                    padding: 0;
                    margin-bottom: 6px;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization {
                    padding: 0 12px 0 0 !important;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience {
                    padding: 0 0 0 12px !important;
                }

                .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience {
                    display: flex;
                    flex-direction: column;
                }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization .label-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience .label-text {
                        height: 16px;
                        margin-bottom: 6px;
                    }

                    .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .specialization .specialization-text, .doctors-wrapper .specialization-detail-wrapper .specialist-display .specialist-item .specialist-detail .specialist-experience .experience .specialization-text {
                        height: 16px;
                    }
}

/*skeleton*/

.planner-doctor-skeleton {
    margin: 50px 0px 10px 95px;
    height: 100%;
    width: 100%;
}

.doctor-title-skeleton {
    height: 100px;
}

.container {
    display: flex;
    width: 100%;
    height: 100px;
    margin: 0%;
}

.sub-container1 {
    width: 50%;
    height: 100px;
}

.sub-container2 {
    width: 50%;
    height: 100px;
    margin: 0px 10% 0px 0px;
}

.doctor-list-skeleton {
    display: flex;
    width: 100%;
    height: 100px;
}

    .doctor-list-skeleton .doct-img {
        height: 100px;
        width: 20%;
    }

    .doctor-list-skeleton .doct-details {
        height: 100px;
        padding-top: 2px;
        width: 60%;
    }

@media (max-width: 600px) {
    .planner-doctor-skeleton {
        display: none;
    }
}
