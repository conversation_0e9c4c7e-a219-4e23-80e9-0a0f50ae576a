@page "/login"
@layout AuthLayout
@using AppointmentPlanner.Client.Models
@using AppointmentPlanner.Client.Services
@inject HttpClient Http
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthService AuthService
@inject AuthStateProvider AuthStateProvider

<PageTitle>Login - Appointment Planner</PageTitle>

<div class="login-form">
    <h2 class="form-title">Welcome Back</h2>
    <p class="form-subtitle">Please sign in to your account</p>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            @errorMessage
        </div>
    }

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i>
            @successMessage
        </div>
    }

    <EditForm Model="loginModel" OnValidSubmit="HandleLogin" FormName="LoginForm">
        <DataAnnotationsValidator />
        
        <div class="form-group">
            <label for="email" class="form-label">
                <i class="fas fa-envelope"></i>
                Email Address
            </label>
            <InputText @bind-Value="loginModel.Email" 
                      class="form-control" 
                      id="email" 
                      placeholder="Enter your email" 
                      autocomplete="email" />
            <ValidationMessage For="@(() => loginModel.Email)" class="validation-message" />
        </div>

        <div class="form-group">
            <label for="password" class="form-label">
                <i class="fas fa-lock"></i>
                Password
            </label>
            <div class="password-input-group">
                <InputText @bind-Value="loginModel.Password" 
                          type="@(showPassword ? "text" : "password")"
                          class="form-control" 
                          id="password" 
                          placeholder="Enter your password" 
                          autocomplete="current-password" />
                <button type="button" 
                        class="password-toggle" 
                        @onclick="TogglePasswordVisibility">
                    <i class="fas @(showPassword ? "fa-eye-slash" : "fa-eye")"></i>
                </button>
            </div>
            <ValidationMessage For="@(() => loginModel.Password)" class="validation-message" />
        </div>

        <div class="form-options">
            <div class="form-check">
                <InputCheckbox @bind-Value="loginModel.RememberMe" 
                              class="form-check-input" 
                              id="rememberMe" />
                <label class="form-check-label" for="rememberMe">
                    Remember me
                </label>
            </div>
            <a href="/forgot-password" class="forgot-password-link">
                Forgot password?
            </a>
        </div>

        <button type="submit" 
                class="btn btn-primary btn-login" 
                disabled="@isLoading">
            @if (isLoading)
            {
                <span class="spinner-border spinner-border-sm" role="status"></span>
                <span>Signing in...</span>
            }
            else
            {
                <i class="fas fa-sign-in-alt"></i>
                <span>Sign In</span>
            }
        </button>
    </EditForm>

    <div class="auth-divider">
        <span>or</span>
    </div>

    <div class="register-link">
        <p>Don't have an account? <a href="/register">Create one here</a></p>
    </div>

    <!-- Demo Accounts -->
    <div class="demo-accounts">
        <h6>Demo Accounts:</h6>
        <div class="demo-buttons">
            <button class="btn btn-outline-secondary btn-sm" @onclick='() => FillDemoCredentials("admin")'>
                Admin Demo
            </button>
            <button class="btn btn-outline-secondary btn-sm" @onclick='() => FillDemoCredentials("doctor")'>
                Doctor Demo
            </button>
            <button class="btn btn-outline-secondary btn-sm" @onclick='() => FillDemoCredentials("patient")'>
                Patient Demo
            </button>
        </div>
    </div>
</div>

@code {
    private LoginModel loginModel = new();
    private bool isLoading = false;
    private bool showPassword = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    protected override void OnInitialized()
    {
        // Check if user is already logged in
        // This would be implemented with proper authentication state
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // Basic validation
            if (string.IsNullOrWhiteSpace(loginModel.Email) || string.IsNullOrWhiteSpace(loginModel.Password))
            {
                errorMessage = "Please enter both email and password.";
                return;
            }

            var result = await AuthService.LoginAsync(loginModel.Email, loginModel.Password);

            if (result.IsSuccess && !string.IsNullOrEmpty(result.Token))
            {
                await AuthStateProvider.LoginAsync(result.Token);

                // Small delay to ensure state is updated
                await Task.Delay(100);

                // Redirect based on role
                var authState = await AuthStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;

                if (user.Identity?.IsAuthenticated == true)
                {
                    if (user.IsInRole("Admin"))
                    {
                        Navigation.NavigateTo("/admin", true);
                    }
                    else if (user.IsInRole("Professional"))
                    {
                        Navigation.NavigateTo("/professional", true);
                    }
                    else if (user.IsInRole("Client"))
                    {
                        Navigation.NavigateTo("/client", true);
                    }
                    else
                    {
                        Navigation.NavigateTo("/", true);
                    }
                }
                else
                {
                    errorMessage = "Authentication failed. Please try again.";
                }
            }
            else
            {
                errorMessage = result.Message ?? "Invalid email or password.";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Login error: {ex.Message}");
            errorMessage = "An error occurred during login. Please try again.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private void FillDemoCredentials(string accountType)
    {
        switch (accountType)
        {
            case "admin":
                loginModel.Email = "<EMAIL>";
                loginModel.Password = "Admin123!";
                break;
            case "doctor":
                loginModel.Email = "<EMAIL>";
                loginModel.Password = "Doctor123!";
                break;
            case "patient":
                loginModel.Email = "<EMAIL>";
                loginModel.Password = "Patient123!";
                break;
        }
        StateHasChanged();
    }
}
