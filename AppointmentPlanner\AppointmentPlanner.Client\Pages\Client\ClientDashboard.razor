@page "/patient/dashboard"
@page "/client/dashboard"
@attribute [Authorize(Roles = "Client")]
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization

<PageTitle>Patient Dashboard - Appointment Planner</PageTitle>

<div class="client-dashboard">
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-user"></i>
            Patient Dashboard
        </h1>
        <p class="dashboard-subtitle">Manage your health and appointments</p>
    </div>

    <div class="row">
        <!-- Patient Statistics -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-primary">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-content">
                    <h3>@upcomingAppointments</h3>
                    <p>Upcoming Appointments</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-success">
                <div class="stat-icon">
                    <i class="fas fa-history"></i>
                </div>
                <div class="stat-content">
                    <h3>@totalAppointments</h3>
                    <p>Total Appointments</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-info">
                <div class="stat-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="stat-content">
                    <h3>@doctorsVisited</h3>
                    <p>Doctors Visited</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-warning">
                <div class="stat-icon">
                    <i class="fas fa-file-medical"></i>
                </div>
                <div class="stat-content">
                    <h3>@medicalRecords</h3>
                    <p>Medical Records</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Appointments -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-calendar-alt"></i>
                        Upcoming Appointments
                    </h5>
                </div>
                <div class="card-body">
                    <div class="appointments-list">
                        @foreach (var appointment in upcomingAppointmentsList)
                        {
                            <div class="appointment-item">
                                <div class="appointment-date">
                                    <div class="date-day">@appointment.Date.Day</div>
                                    <div class="date-month">@appointment.Date.ToString("MMM")</div>
                                </div>
                                <div class="appointment-content">
                                    <h6 class="doctor-name">@appointment.DoctorName</h6>
                                    <p class="appointment-type">@appointment.Type</p>
                                    <p class="appointment-time">
                                        <i class="fas fa-clock"></i>
                                        @appointment.Time
                                    </p>
                                    <p class="appointment-location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        @appointment.Location
                                    </p>
                                </div>
                                <div class="appointment-actions">
                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewDetails(appointment.Id)">
                                        <i class="fas fa-eye"></i>
                                        View
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" @onclick="() => RescheduleAppointment(appointment.Id)">
                                        <i class="fas fa-edit"></i>
                                        Reschedule
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => CancelAppointment(appointment.Id)">
                                        <i class="fas fa-times"></i>
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Profile -->
        <div class="col-lg-4 mb-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <button class="btn btn-primary btn-action" @onclick="BookAppointment">
                            <i class="fas fa-plus"></i>
                            Book Appointment
                        </button>
                        <button class="btn btn-success btn-action" @onclick="ViewMedicalRecords">
                            <i class="fas fa-file-medical-alt"></i>
                            Medical Records
                        </button>
                        <button class="btn btn-info btn-action" @onclick="FindDoctors">
                            <i class="fas fa-search"></i>
                            Find Doctors
                        </button>
                        <button class="btn btn-warning btn-action" @onclick="UpdateProfile">
                            <i class="fas fa-user-edit"></i>
                            Update Profile
                        </button>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-user-circle"></i>
                        Patient Profile
                    </h5>
                </div>
                <div class="card-body">
                    <div class="profile-info">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-details">
                            <h6>John Patient</h6>
                            <p class="age">Age: 35 years</p>
                            <p class="blood-group">Blood Group: O+</p>
                            <p class="phone">+****************</p>
                            <p class="email"><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Medical History -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-history"></i>
                        Recent Medical History
                    </h5>
                </div>
                <div class="card-body">
                    <div class="history-list">
                        @foreach (var record in recentHistory)
                        {
                            <div class="history-item">
                                <div class="history-date">
                                    <small>@record.Date.ToString("MMM dd, yyyy")</small>
                                </div>
                                <div class="history-content">
                                    <h6>@record.Title</h6>
                                    <p>@record.Doctor</p>
                                    <small class="text-muted">@record.Notes</small>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Health Tips -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-lightbulb"></i>
                        Health Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Patient Features:</strong> Appointment booking, medical history access, doctor search, prescription management, and health tracking tools.
                    </div>
                    <div class="health-tips">
                        <div class="tip-item">
                            <i class="fas fa-heart text-danger"></i>
                            <span>Regular exercise helps maintain cardiovascular health</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-apple-alt text-success"></i>
                            <span>Eat a balanced diet rich in fruits and vegetables</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-bed text-primary"></i>
                            <span>Get 7-8 hours of quality sleep each night</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .client-dashboard {
        padding: 20px;
    }

    .dashboard-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .dashboard-title {
        color: #333;
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .dashboard-title i {
        color: #17a2b8;
        margin-right: 15px;
    }

    .dashboard-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .stat-primary .stat-icon { background: #007bff; }
    .stat-success .stat-icon { background: #28a745; }
    .stat-info .stat-icon { background: #17a2b8; }
    .stat-warning .stat-icon { background: #ffc107; }

    .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        color: #333;
    }

    .stat-content p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .appointments-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .appointment-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .appointment-item:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .appointment-date {
        text-align: center;
        min-width: 60px;
        padding: 10px;
        background: #007bff;
        color: white;
        border-radius: 8px;
    }

    .date-day {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
    }

    .date-month {
        font-size: 0.8rem;
        text-transform: uppercase;
    }

    .appointment-content {
        flex: 1;
    }

    .doctor-name {
        margin: 0 0 5px 0;
        color: #333;
        font-weight: 600;
    }

    .appointment-type {
        margin: 0 0 5px 0;
        color: #007bff;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .appointment-time, .appointment-location {
        margin: 0 0 3px 0;
        color: #6c757d;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .appointment-actions {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .quick-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .btn-action {
        padding: 12px 15px;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-start;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateX(5px);
    }

    .profile-info {
        display: flex;
        align-items: flex-start;
        gap: 15px;
    }

    .profile-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #17a2b8;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .profile-details h6 {
        margin: 0 0 8px 0;
        color: #333;
        font-weight: 600;
    }

    .profile-details p {
        margin: 0 0 5px 0;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .history-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .history-item {
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .history-item:last-child {
        border-bottom: none;
    }

    .history-date {
        margin-bottom: 5px;
    }

    .history-content h6 {
        margin: 0 0 5px 0;
        color: #333;
        font-weight: 600;
    }

    .history-content p {
        margin: 0 0 5px 0;
        color: #007bff;
        font-size: 0.9rem;
    }

    .health-tips {
        margin-top: 15px;
    }

    .tip-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 0;
        font-size: 0.9rem;
        color: #495057;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 10px 10px 0 0 !important;
    }

    .card-title {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .card-title i {
        color: #17a2b8;
        margin-right: 10px;
    }
</style>

@code {
    private int upcomingAppointments = 3;
    private int totalAppointments = 24;
    private int doctorsVisited = 8;
    private int medicalRecords = 15;

    private List<AppointmentItem> upcomingAppointmentsList = new()
    {
        new AppointmentItem { Id = 1, DoctorName = "Dr. Sarah Johnson", Type = "Cardiology Consultation", Date = DateTime.Today.AddDays(2), Time = "10:00 AM - 10:30 AM", Location = "Room 201, Cardiology Wing" },
        new AppointmentItem { Id = 2, DoctorName = "Dr. Michael Brown", Type = "General Checkup", Date = DateTime.Today.AddDays(5), Time = "2:00 PM - 2:30 PM", Location = "Room 105, General Medicine" },
        new AppointmentItem { Id = 3, DoctorName = "Dr. Emily Davis", Type = "Follow-up", Date = DateTime.Today.AddDays(7), Time = "11:00 AM - 11:30 AM", Location = "Room 301, Neurology Wing" }
    };

    private List<MedicalRecord> recentHistory = new()
    {
        new MedicalRecord { Date = DateTime.Today.AddDays(-7), Title = "Routine Checkup", Doctor = "Dr. Sarah Johnson", Notes = "Blood pressure normal, recommended regular exercise" },
        new MedicalRecord { Date = DateTime.Today.AddDays(-30), Title = "Blood Test", Doctor = "Dr. Michael Brown", Notes = "All parameters within normal range" },
        new MedicalRecord { Date = DateTime.Today.AddDays(-60), Title = "Consultation", Doctor = "Dr. Emily Davis", Notes = "Prescribed medication for headaches" }
    };

    private void ViewDetails(int appointmentId)
    {
        // Navigate to appointment details
    }

    private void RescheduleAppointment(int appointmentId)
    {
        // Open reschedule dialog
    }

    private void CancelAppointment(int appointmentId)
    {
        // Cancel appointment
    }

    private void BookAppointment()
    {
        // Navigate to appointment booking
    }

    private void ViewMedicalRecords()
    {
        // Navigate to medical records
    }

    private void FindDoctors()
    {
        // Navigate to doctor search
    }

    private void UpdateProfile()
    {
        // Navigate to profile update
    }

    public class AppointmentItem
    {
        public int Id { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Time { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
    }

    public class MedicalRecord
    {
        public DateTime Date { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Doctor { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
    }
}
