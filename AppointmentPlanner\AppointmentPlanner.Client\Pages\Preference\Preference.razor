﻿@page "/preference"

@using AppointmentPlanner.Data;
@using AppointmentPlanner.Models;

@if (isDataLoaded)
{
<div id="preference" class='preference-container'>
    <header>
        <div class="module-title">
            <div class='title'>PREFERENCE</div>
            <div class='underline'></div>
        </div>
    </header>
    <div class="control-container">
        <div class='label-text'>Default View</div>
        <SfDropDownList CssClass='preference-drop-down' ID='CurrentView' @bind-Value="@selectedView" Width="335px" DataSource="@views">
            <DropDownListEvents TItem="TextValueData" TValue="string" ValueChange="onViewChange"></DropDownListEvents>
            <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="control-container">
        <div class='label-text'>Calendar Start Time</div>
        <SfDropDownList CssClass='preference-drop-down' ID='CalendarStart' @bind-Value="@selectedStartHour" Width="335px" DataSource="@startHours">
            <DropDownListEvents TItem="TextValueData" TValue="string" ValueChange="onStartHourChange"></DropDownListEvents>
            <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="control-container">
        <div class='label-text'>Calendar End Time</div>
        <SfDropDownList CssClass='preference-drop-down' ID='CalendarEnd' @bind-Value="@selectedEndHour" Width="335px" DataSource="@endHours">
            <DropDownListEvents TItem="TextValueData" TValue="string" ValueChange="onEndhourChange"></DropDownListEvents>
            <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="control-container">
        <div class='label-text'>Slot Duration</div>
        <SfDropDownList CssClass='preference-drop-down' ID='Duration' @bind-Value="@timeInterval" Width="335px" DataSource="@timeSlots">
            <DropDownListEvents TItem="TextValueNumericData" TValue="int?" ValueChange="onDurationChange"></DropDownListEvents>
            <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="control-container">
        <div class='label-text'>Booking Color</div>
        <SfDropDownList CssClass='preference-drop-down' ID='BookingColor' @bind-Value="@selectedCategory" Width="335px" DataSource="@colorCategory">
            <DropDownListEvents TItem="TextValueData" TValue="string" ValueChange="onColorChange"></DropDownListEvents>
            <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="control-container">
        <div class='label-text'>First Day of the Week</div>
        <SfDropDownList CssClass='preference-drop-down' ID='FirstDayOfWeek' @bind-Value="@selectedDayOfWeek" Width="335px" DataSource="@dayOfWeeks">
            <DropDownListEvents TItem="TextValueNumericData" TValue="int?" ValueChange="onFirstDayChange"></DropDownListEvents>
            <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
</div>
}
else
{
    <div class="planner-preference-skeleton">
        <div class="preference-container1">
            <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="15px" Width="97%"></SfSkeleton>
            <br/>
            <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="15px" Width="74%"></SfSkeleton>
        </div>
        <div class="preference-container2">
            @for(int i=0; i<6; i++){
                <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="15px" Width="12%"></SfSkeleton>
                <br/>
                <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="15px" Width="35%"></SfSkeleton>
                <br/><br/>
            }
        </div>
    </div>    
}

@code{
    [Inject]
    protected AppointmentService Service { get; set; }
    private List<TextValueData> views { get; set; }
    private List<TextValueData> startHours { get; set; }
    private List<TextValueData> endHours { get; set; }
    private List<TextValueNumericData> timeSlots { get; set; }
    private List<TextValueData> colorCategory { get; set; }
    private List<TextValueNumericData> dayOfWeeks { get; set; }
    private string selectedView { get; set; }
    private string selectedStartHour { get; set; }
    private string selectedEndHour { get; set; }
    private string selectedCategory { get; set; }
    private int? timeInterval { get; set; }
    private int? selectedDayOfWeek { get; set; }
    private bool isDataLoaded;

    protected override void OnInitialized()
    {
        this.views = Service.Views;
        this.startHours = Service.StartHours;
        this.endHours = Service.EndHours;
        this.timeSlots = Service.TimeSlot;
        this.colorCategory = Service.ColorCategory;
    }

    private void onViewChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<string, TextValueData> args)
    {
        Service.CalendarSettings.CurrentView = args.Value;
    }
    private void onStartHourChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<string, TextValueData> args)
    {
        Service.CalendarSettings.Calendar.Start = args.Value;
    }
    private void onEndhourChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<string, TextValueData> args)
    {
        Service.CalendarSettings.Calendar.End = args.Value;
    }
    private void onColorChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<string, TextValueData> args)
    {
        Service.CalendarSettings.BookingColor = (string)args.Value;
    }
    private void onDurationChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, TextValueNumericData> args)
    {
        Service.CalendarSettings.Interval = (int)args.Value;
    }
    private void onFirstDayChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, TextValueNumericData> args)
    {
        Service.CalendarSettings.FirstDayOfWeek = (int)args.Value;
    }

    private async void LoadData()
    {   
        this.dayOfWeeks = Service.DayOfWeekList;
        this.selectedView = Service.CalendarSettings.CurrentView;
        this.selectedStartHour = Service.CalendarSettings.Calendar.Start;
        this.selectedEndHour = Service.CalendarSettings.Calendar.End;
        this.selectedCategory = Service.CalendarSettings.BookingColor;
        this.timeInterval = Service.CalendarSettings.Interval;
        this.selectedDayOfWeek = Service.CalendarSettings.FirstDayOfWeek;
        await Task.Delay(100);
        isDataLoaded = true;
        StateHasChanged();
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            LoadData();
        }
    }
}