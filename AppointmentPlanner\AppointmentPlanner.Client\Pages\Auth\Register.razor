@page "/register"
@layout AuthLayout
@using AppointmentPlanner.Models
@inject HttpClient Http
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Register - Appointment Planner</PageTitle>

<div class="register-form">
    <h2 class="form-title">Create Account</h2>
    <p class="form-subtitle">Join our healthcare platform</p>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            @errorMessage
        </div>
    }

    <EditForm Model="registerModel" OnValidSubmit="HandleRegister" FormName="RegisterForm">
        <DataAnnotationsValidator />
        
        <!-- Role Selection -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-user-tag"></i>
                Account Type
            </label>
            <div class="role-selection">
                <InputRadioGroup @bind-Value="registerModel.Role">
                    <div class="role-option">
                        <InputRadio Value='"Client"' id="roleClient" />
                        <label for="roleClient" class="role-label">
                            <i class="fas fa-user"></i>
                            <span>Patient/Client</span>
                            <small>Book appointments and manage health records</small>
                        </label>
                    </div>
                    <div class="role-option">
                        <InputRadio Value='"Professional"' id="roleProfessional" />
                        <label for="roleProfessional" class="role-label">
                            <i class="fas fa-user-md"></i>
                            <span>Healthcare Professional</span>
                            <small>Manage appointments and patient care</small>
                        </label>
                    </div>
                </InputRadioGroup>
            </div>
        </div>

        <!-- Personal Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="firstName" class="form-label">
                        <i class="fas fa-user"></i>
                        First Name
                    </label>
                    <InputText @bind-Value="registerModel.FirstName" 
                              class="form-control" 
                              id="firstName" 
                              placeholder="Enter first name" />
                    <ValidationMessage For="@(() => registerModel.FirstName)" class="validation-message" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="lastName" class="form-label">
                        <i class="fas fa-user"></i>
                        Last Name
                    </label>
                    <InputText @bind-Value="registerModel.LastName" 
                              class="form-control" 
                              id="lastName" 
                              placeholder="Enter last name" />
                    <ValidationMessage For="@(() => registerModel.LastName)" class="validation-message" />
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="email" class="form-label">
                <i class="fas fa-envelope"></i>
                Email Address
            </label>
            <InputText @bind-Value="registerModel.Email" 
                      class="form-control" 
                      id="email" 
                      placeholder="Enter email address" 
                      autocomplete="email" />
            <ValidationMessage For="@(() => registerModel.Email)" class="validation-message" />
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <div class="password-input-group">
                        <InputText @bind-Value="registerModel.Password" 
                                  type="@(showPassword ? "text" : "password")"
                                  class="form-control" 
                                  id="password" 
                                  placeholder="Enter password" />
                        <button type="button" 
                                class="password-toggle" 
                                @onclick="TogglePasswordVisibility">
                            <i class="fas @(showPassword ? "fa-eye-slash" : "fa-eye")"></i>
                        </button>
                    </div>
                    <ValidationMessage For="@(() => registerModel.Password)" class="validation-message" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">
                        <i class="fas fa-lock"></i>
                        Confirm Password
                    </label>
                    <InputText @bind-Value="registerModel.ConfirmPassword" 
                              type="password"
                              class="form-control" 
                              id="confirmPassword" 
                              placeholder="Confirm password" />
                    <ValidationMessage For="@(() => registerModel.ConfirmPassword)" class="validation-message" />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="phoneNumber" class="form-label">
                        <i class="fas fa-phone"></i>
                        Phone Number
                    </label>
                    <InputText @bind-Value="registerModel.PhoneNumber" 
                              class="form-control" 
                              id="phoneNumber" 
                              placeholder="Enter phone number" />
                    <ValidationMessage For="@(() => registerModel.PhoneNumber)" class="validation-message" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="dateOfBirth" class="form-label">
                        <i class="fas fa-calendar"></i>
                        Date of Birth
                    </label>
                    <InputDate @bind-Value="registerModel.DateOfBirth" 
                              class="form-control" 
                              id="dateOfBirth" />
                    <ValidationMessage For="@(() => registerModel.DateOfBirth)" class="validation-message" />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="gender" class="form-label">
                        <i class="fas fa-venus-mars"></i>
                        Gender
                    </label>
                    <InputSelect @bind-Value="registerModel.Gender" class="form-control" id="gender">
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </InputSelect>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="address" class="form-label">
                        <i class="fas fa-map-marker-alt"></i>
                        Address
                    </label>
                    <InputText @bind-Value="registerModel.Address" 
                              class="form-control" 
                              id="address" 
                              placeholder="Enter address" />
                </div>
            </div>
        </div>

        <!-- Professional-specific fields -->
        @if (registerModel.Role == "Professional")
        {
            <div class="professional-fields">
                <h5 class="section-title">Professional Information</h5>
                
                @if (registerModel.DoctorProfile == null)
                {
                    registerModel.DoctorProfile = new DoctorRegistrationModel();
                }

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="education" class="form-label">
                                <i class="fas fa-graduation-cap"></i>
                                Education
                            </label>
                            <InputText @bind-Value="registerModel.DoctorProfile.Education" 
                                      class="form-control" 
                                      id="education" 
                                      placeholder="e.g., MBBS, MD" />
                            <ValidationMessage For="@(() => registerModel.DoctorProfile.Education)" class="validation-message" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="specialization" class="form-label">
                                <i class="fas fa-stethoscope"></i>
                                Specialization
                            </label>
                            <InputSelect @bind-Value="registerModel.DoctorProfile.Specialization" class="form-control" id="specialization">
                                <option value="">Select Specialization</option>
                                <option value="generalmedicine">General Medicine</option>
                                <option value="neurology">Neurology</option>
                                <option value="dermatology">Dermatology</option>
                                <option value="orthopedics">Orthopedics</option>
                                <option value="diabetology">Diabetology</option>
                                <option value="cardiology">Cardiology</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => registerModel.DoctorProfile.Specialization)" class="validation-message" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="experience" class="form-label">
                                <i class="fas fa-clock"></i>
                                Experience
                            </label>
                            <InputSelect @bind-Value="registerModel.DoctorProfile.Experience" class="form-control" id="experience">
                                <option value="">Select Experience</option>
                                <option value="1+ years">1+ years</option>
                                <option value="2+ years">2+ years</option>
                                <option value="5+ years">5+ years</option>
                                <option value="10+ years">10+ years</option>
                                <option value="15+ years">15+ years</option>
                                <option value="20+ years">20+ years</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => registerModel.DoctorProfile.Experience)" class="validation-message" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="designation" class="form-label">
                                <i class="fas fa-id-badge"></i>
                                Designation
                            </label>
                            <InputText @bind-Value="registerModel.DoctorProfile.Designation" 
                                      class="form-control" 
                                      id="designation" 
                                      placeholder="e.g., Senior Specialist" />
                        </div>
                    </div>
                </div>
            </div>
        }

        <button type="submit" 
                class="btn btn-primary btn-register" 
                disabled="@isLoading">
            @if (isLoading)
            {
                <span class="spinner-border spinner-border-sm" role="status"></span>
                <span>Creating Account...</span>
            }
            else
            {
                <i class="fas fa-user-plus"></i>
                <span>Create Account</span>
            }
        </button>
    </EditForm>

    <div class="login-link">
        <p>Already have an account? <a href="/login">Sign in here</a></p>
    </div>
</div>

@code {
    private RegisterModel registerModel = new() { Role = "Client" };
    private bool isLoading = false;
    private bool showPassword = false;
    private string errorMessage = string.Empty;

    private async Task HandleRegister()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // TODO: Replace with actual API call
            var response = await Http.PostAsJsonAsync("/api/auth/register", registerModel);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<AuthResult>();
                if (result?.Success == true)
                {
                    Navigation.NavigateTo("/login?message=Registration successful. Please log in.");
                }
                else
                {
                    errorMessage = result?.Message ?? "Registration failed";
                }
            }
            else
            {
                errorMessage = "Registration failed. Please try again.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during registration. Please try again.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }
}
