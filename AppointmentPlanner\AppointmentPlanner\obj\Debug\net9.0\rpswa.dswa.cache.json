{"GlobalPropertiesHash": "mKwnf+0VL291hC1svd6HhwxoZ+tweIM4eRfIEKTAe3w=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["LYfNqU2QAvJ/ZaP3I3sQNTvWhbfrN2/IJiEg3y7CPXM=", "Tib5eNTxlk+kY/aodjqHIFICfvKv6GS0OWZDuoMZ+WY=", "Z9v99J1cDmCBSRDOvg9/zAvkhZ24gcPlOAsIP1QxqT0=", "XVBQYoOfwLkLgzMcjuKRO5O9n1SMzfbKSvlJHo9k5vs=", "tkgAbHKhTmdXBEQFGutzLGg74LKj9qU2mbUsH5TUq4Q=", "Oe8Fjx1CfoBmN8AFBlBnD3xVpQyHcRYXVro4K4nl8Ag=", "C9Qqds4uNZnRXZFIml17mKkXcVobZrzdOtGjtTp001I=", "5UVMH67FxS6DXuKkHUPg5BvTy5WfANo//E26IQdRQrQ=", "1HqrWaHVsZJMdb1gz4cJK6JqX1oOKMgrp7pK12nnYwI=", "B10yZc+/3TKJ6Bhx1gWJ/gTUr9zehpatVngAz4nccLI=", "d5H7gSijdz3iOg8IXrzzemnXIx3hdbSBaJeh+MOtEhs=", "mr02przHiMKulEa8G8Um236NcrKSB18z+UOna13CR20=", "dYVPran7lMRfZtzt71A3jRjeZ2ZFmeNSOBWwwIFLBLg=", "mwVpPd6jAwwDiK00+ZwBVRnKpeyy4YFxr/GYxiPwOgY=", "Myno3NLWsiQARsc8Y6lk3XM7sx5+l2WxLk27v9XtTlg=", "o3B2gSxggpiEBeUzLGTtCpCk9XVg/f5JDfs7WVmAfkY=", "kpH1dNAL7FyYk2dGKaoWK2fS2hfp6/W9LHY/XR+8HkU=", "GlX68Y0mR51zVE89H+tAewBnkQDoEB5rgMrl9v3eBkI=", "VauDCSe0h5DL4BlOqeEZSyR1h7AFdc0a98zJFcyqMyk=", "Q705Pj3fLcO1X6N1XrtbbVg3ZhlIIXtXyoRNiXvqln4=", "abl3OWq8vxYCAhuqGXoBlvOatb58IgKbyTsNho0VCko=", "3JF9eTs0gtTASl84y+kro5nptiktcDcbXzLHvyr72EI=", "aV0SIOqEpwvWOAku3mIUokEtwh1kPqw2E5ePWdZ/imA=", "6IS2U17KRR/8jg/yiQfsJUty/f4HmTcj8T91UGzvOE0=", "XYSHmyj6kf3AH/1D7PirEDEIB1xerzQOR8Up1bY7SK4=", "O63bFRUgPu1COT+M3yW82fYdgc752Mq7LynLbA76m5s=", "Hl2Aup4xZVLF1UY26iLXfAwpTd90RFn4SfCs/3DT4nY=", "fIY+Vwg4LwmmToRfMQ66nsjwJqTE4YIgeGbmOmW4rpo=", "k0/I1hQAFtXjoZpPcAhQ+uhHfSwbu7Eos2zK2kS/TQE=", "ZwGX+JIuSJ4ADbt7IT52HJVjPHl7ReBktNpc0leoTyk=", "hnG0dmAQNGHlRp8g469ujAFITKuxHcm1Uip8BFmOFHY=", "6V+pU7n++uOlt/UESW5zY0lNqWqqTBGmIibGE0z4xyc=", "z0wErNvWx7UyHelGxFwARSv+2viyEq4S87R3mkj1kjs=", "xZJm3kVCBC0AQymkAUbGm8XHNgdJUns/lyMkVaaDIbc=", "49vgQV6ZTVcbiX46gYy+2lbBi4Br49jBJnvQtGwvyww=", "AQfnz1TAP5ig577urpXrLadnP9iTwNOKt3iNXdPX9OU=", "WyW884cBJKVtl/6JixrV1ePtfe8qodDJOOpVFg57rAU=", "ekIxIv9/OtSb6w4ouFA3uD8zwNTgVXT8ajQIhkiGLak=", "3ebOc8Uz++v0FxntjxfLYxPAaSCA74cvUhjUKKUQB5E=", "zDeoFG5vS2dBht889SH2TmdoqLWvL9a3noebrm3V5eM=", "D8YCDzgA8B59OTG+cvHXBQfnKZBL29rdOxhwIl6TGEQ=", "GVVTXhmP2x4WGQDGPSCbGE02GWYvLPOXDbsWEZmczN8=", "8b7poMoACfLJ0ys+OGPrV9rWYiDzwOxehzMpIh+vbIg=", "0Af4a7YV7kZX+tLeq9Ndi0m5ZH+QOiwwXvhvR5+n/Fw=", "rE7NvdbAhZnZoI0mzXdCG/sQ4m9V0kNgBkKoI2jfiMQ=", "qBN/CPZXpu8SUFEtPS28lwvwy+Ut+mRJ3kJG6S6KSoM=", "8ulxOmQw1pLVopdakwd/tsVXUBYv1WD3VOM/YPV+M/0=", "95yxfHzaLyhvKUbkRMakNsMG0q5I3mFFbDbZbt6WBOI=", "AVlxeqmpT70nQ86uQB4CTNSHJFZ7eePBVD8mFgfHhQ4=", "184DU0P3BgAjHhuQ3cKPc8KIFRwqgEx/Dk6cL6aJetA=", "xqOKxl9uwWXbg9CHnnzJqyZ1ajFSA2pRmqEX5HYdu10=", "YYp29ZQ2BxQaVv2zQCoSz4S8JsrbS1M4tN8UK5qZyv4=", "2gynKh7sa1Wqsnr3vYItnjF2dHR6cheOevXyDfKn4nE=", "ZHkxecEa3jl+1p+gthQZbEWl09aOIQmjsjaYn7Tw4SQ=", "3JOwzVnyDOJ2S1EeQ3HerF7kLaWLFe0nkHwwQuE+H+w=", "M6wLVbp6poQH/2bTO0DRBdYCq4YDsMP6bf87qFQtqOk=", "7Kq8xzfAtAO9cHMAXaTpE4rmpf0u68wZd5s5uNX29gQ=", "6vsvQe8qhj9+jXx4jYOnHC95Jp06fbgT/ZQMuJCYVTI=", "3um/Qnu4pFrSCrzXqE9jxOOIjSwgxqOmIGtSllIguRM=", "3weaiWg/2cIbiX7uBijwQwxkb9KP7RSqA0EVp4hHm3A=", "WMJEg9FlBhuRUeG+05k0RjMLzvRXPIFy+RMOOOhhCRs=", "Slr48DUfS+xc3uYGcZKqJe43ijujJCNiivn8XAkEtVY=", "oTUZrYy3/a1K62dt+EeNcqlpyF1AhOlYmaISlFNs1Ks=", "VhvgPlSlQHE691SlH03pfl2sRbDVEUJ5rZtUn+E1IaA=", "NFLJ03Q5iXMdOntKDmP5mjeAiOXyHWOBjuGe72oDwcc=", "qLt3YsUeq6OtCYCyuDzcNzaIPqNLtldIwB7EqqiQka8=", "g20qmdt69s21oCwP5DbqvnPGXsKtVKCoI50ojUSg1QA=", "38jI9Mkdu/kRnZ2bRg00mfV9+lo/KC7QKAx71UtdObI=", "8978K/tqjWamoPnaZgY5A6bkmJ78jYnJuiXjpTN5QtM="], "CachedAssets": {"LYfNqU2QAvJ/ZaP3I3sQNTvWhbfrN2/IJiEg3y7CPXM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\app.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0mzc86pppx", "Integrity": "SO/fuIIWUxoIu1OABrOpK5n/AvzxJcg+0H3PT1eutAE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 861, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "Tib5eNTxlk+kY/aodjqHIFICfvKv6GS0OWZDuoMZ+WY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\about.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/about#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pq6w9s9gq7", "Integrity": "IRNpaHHUtkUiecSqdwfSvjEsQE7laVqPTrxH3Eoubh8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\about.css", "FileLength": 3689, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "Z9v99J1cDmCBSRDOvg9/zAvkhZ24gcPlOAsIP1QxqT0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\.gitkeep", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/#[.{fingerprint}]?.gitkeep", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\.gitkeep", "FileLength": 0, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "XVBQYoOfwLkLgzMcjuKRO5O9n1SMzfbKSvlJHo9k5vs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\favicon.ico", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mjoy6y12me", "Integrity": "ws41UmW5nFdrGhq8/ZNWMXQfw5l9UZPl3rwG6waxZqo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\favicon.ico", "FileLength": 6574, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "tkgAbHKhTmdXBEQFGutzLGg74LKj9qU2mbUsH5TUq4Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.eot", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icomoon#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k7gchpbwir", "Integrity": "EEJidA+0V9RhZYlx76rGwJtaUCzrgC1D2a0S/klIWBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.eot", "FileLength": 6984, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "Oe8Fjx1CfoBmN8AFBlBnD3xVpQyHcRYXVro4K4nl8Ag=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icomoon#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "czshin8ypa", "Integrity": "+LpcV0ac/I9o/0YDgQuWZaq7Jgr/RfdVB9lm4YpLrvw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.svg", "FileLength": 22564, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "C9Qqds4uNZnRXZFIml17mKkXcVobZrzdOtGjtTp001I=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.ttf", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icomoon#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t8cib87j9q", "Integrity": "fUlVwvis8FEm5Pf3Vi7W3bBAPQw5LwxdsEV8K/kpcUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.ttf", "FileLength": 6820, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "5UVMH67FxS6DXuKkHUPg5BvTy5WfANo//E26IQdRQrQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.woff", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icomoon#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "20uwh6uym7", "Integrity": "Hrf6NbXWrbDj5eS2GmLs1T5w1yR5pRbySQPDpl3S/k0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icomoon.woff", "FileLength": 6896, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "1HqrWaHVsZJMdb1gz4cJK6JqX1oOKMgrp7pK12nnYwI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icons.eot", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icons#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3d8lplpsl7", "Integrity": "aSXI21gdxGKa08ETs78vL+8y1bMTX9bv22BmSYjbpRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icons.eot", "FileLength": 29196, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "B10yZc+/3TKJ6Bhx1gWJ/gTUr9zehpatVngAz4nccLI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icons.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icons#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "643dve4qr9", "Integrity": "BoiG+cNEHLABDlrIFxU2pMmTcr5P/oiKtt2UnvRPPrw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icons.svg", "FileLength": 126834, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "d5H7gSijdz3iOg8IXrzzemnXIx3hdbSBaJeh+MOtEhs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icons.ttf", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icons#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q9vrzryka0", "Integrity": "DkaberwavTg9zFCIZHEoV5ttxughiAwJXxceRsjQhbE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icons.ttf", "FileLength": 29032, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "mr02przHiMKulEa8G8Um236NcrKSB18z+UOna13CR20=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\fonts\\icons.woff", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/fonts/icons#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tw6rdpaijf", "Integrity": "Q1PPF3BfPO+OQrtVJ31u51cu5wzpDwZCk3KfUtqVhNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\fonts\\icons.woff", "FileLength": 29108, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "dYVPran7lMRfZtzt71A3jRjeZ2ZFmeNSOBWwwIFLBLg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\Icons\\Doctors.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/Icons/Doctors#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqwa8ake9d", "Integrity": "zQvv8+1bOnWIiTYjb1A0QdCTZDsMIH6799XI2PkdsIY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\Icons\\Doctors.svg", "FileLength": 1148, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "mwVpPd6jAwwDiK00+ZwBVRnKpeyy4YFxr/GYxiPwOgY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\Icons\\ThisWeek_Widget.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/Icons/ThisWeek_Widget#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tosvj9oyn7", "Integrity": "/Vn+cQw80ikpz7yjv8EL2CEc++XtvyTZjkRWkRptP9g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\Icons\\ThisWeek_Widget.svg", "FileLength": 2171, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "Myno3NLWsiQARsc8Y6lk3XM7sx5+l2WxLk27v9XtTlg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\Icons\\Today_Widget.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/Icons/Today_Widget#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "33ra4wkyza", "Integrity": "YZSgvOEvl5vDe3w5MaHD/uhWxGvWz74YDzEiA1T9jOA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\Icons\\Today_Widget.svg", "FileLength": 1686, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "o3B2gSxggpiEBeUzLGTtCpCk9XVg/f5JDfs7WVmAfkY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\Admin.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/Admin#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eyjlzj95rg", "Integrity": "Rlf0eHEuYJsQsgCt4Gtjhx5+7EW6bnOLXSniMoC+A8c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\Admin.png", "FileLength": 89237, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "kpH1dNAL7FyYk2dGKaoWK2fS2hfp6/W9LHY/XR+8HkU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\AlexaRichardson.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/AlexaRichardson#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "knjf101i5m", "Integrity": "wL0+kR45ULnNJt/oKoRPpZSp1D8AAOeRGe9cUcG/eA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\AlexaRichardson.png", "FileLength": 72456, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "GlX68Y0mR51zVE89H+tAewBnkQDoEB5rgMrl9v3eBkI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\AmeliaEdwards.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/AmeliaEdwards#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v9qevix96", "Integrity": "cL4JD2oZjndp3FJMNWgWBScZ3ZtTsS4ZbtY66GSYFUE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\AmeliaEdwards.png", "FileLength": 75795, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "VauDCSe0h5DL4BlOqeEZSyR1h7AFdc0a98zJFcyqMyk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\default.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/default#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9xeef7lwya", "Integrity": "84Fst1H0cP+nwfgNR9BtaHtnHdFHwt+CuvunNNlXUcc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\default.png", "FileLength": 8649, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "Q705Pj3fLcO1X6N1XrtbbVg3ZhlIIXtXyoRNiXvqln4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\MollieCobb.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/MollieCobb#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3t6b16schd", "Integrity": "yc9ozFoAtpvHJMrZ8IAnJ5PXroUHjkkJgtMQeKyNIc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\MollieCobb.png", "FileLength": 88097, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "abl3OWq8vxYCAhuqGXoBlvOatb58IgKbyTsNho0VCko=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\NemboLukni.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/NemboLukni#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9t82j86gp", "Integrity": "H7QFxMN0aMUf5djAJOYqjl1mFpfjVPHSJQCkZast0L8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\NemboLukni.png", "FileLength": 59556, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "3JF9eTs0gtTASl84y+kro5nptiktcDcbXzLHvyr72EI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\NoutGolstein.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/NoutGolstein#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8gs6pthdlh", "Integrity": "iHKMm+cn1gkRI2CP66GnMokRSb1uaumhC9gxWrMG29U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\NoutGolstein.png", "FileLength": 73049, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "aV0SIOqEpwvWOAku3mIUokEtwh1kPqw2E5ePWdZ/imA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\PaulWalker.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/<PERSON>#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vc426p8siq", "Integrity": "cTOZRFKPXobpbbD0JuWvOwKzFdWS+V4pKFVAOlnVq+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\PaulWalker.png", "FileLength": 78641, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "6IS2U17KRR/8jg/yiQfsJUty/f4HmTcj8T91UGzvOE0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\images\\YaraBarros.png", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/images/YaraBarros#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p1vk8g290j", "Integrity": "LYKgnA3YQcUz4DWGkkAt8LDjawN4qQsSxp/jqZcCpXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\images\\YaraBarros.png", "FileLength": 67828, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "XYSHmyj6kf3AH/1D7PirEDEIB1xerzQOR8Up1bY7SK4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\styles\\bootstrap.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/styles/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sojl342i4j", "Integrity": "FJZ6VUy952aQ8PATbd3Xb57wHwX10rykt5gT8XXfEqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\styles\\bootstrap.css", "FileLength": 1866908, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "O63bFRUgPu1COT+M3yW82fYdgc752Mq7LynLbA76m5s=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\assets\\styles\\bootstrap.scss", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/assets/styles/bootstrap#[.{fingerprint}]?.scss", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pakh32jgpx", "Integrity": "AdydQGLGQjBP0R8wPelhNFonPYFPUSxvm8aOqoHf/6s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\assets\\styles\\bootstrap.scss", "FileLength": 2045571, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "Hl2Aup4xZVLF1UY26iLXfAwpTd90RFn4SfCs/3DT4nY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\calendar.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/calendar#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8hkgl31fj", "Integrity": "RIgeIViUQ2sbX7vDIymhf3NbxJTIn59rVUEKAsyVZYU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\calendar.css", "FileLength": 24618, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "fIY+Vwg4LwmmToRfMQ66nsjwJqTE4YIgeGbmOmW4rpo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\common.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/common#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z3tem4247x", "Integrity": "3xDbowbs6Yg/Zq+tRyWb/whL255zQgS8I3napMX+Qcg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\common.css", "FileLength": 1722, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "k0/I1hQAFtXjoZpPcAhQ+uhHfSwbu7Eos2zK2kS/TQE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\dashboard.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qefsxcgft7", "Integrity": "Am6BntRw9mc0LRx0/1TUFDSPVSyba7U7E6vnneCzjyQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\dashboard.css", "FileLength": 8984, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "ZwGX+JIuSJ4ADbt7IT52HJVjPHl7ReBktNpc0leoTyk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\doctor.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/doctor#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0ih9uwfz8c", "Integrity": "KDGHTOLBc8jpeJUmkC7jxqtFj4CpaX5ZfxJlAqogjVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\doctor.css", "FileLength": 15146, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "hnG0dmAQNGHlRp8g469ujAFITKuxHcm1Uip8BFmOFHY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\doctordetails.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/doctordetails#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ahye65sfro", "Integrity": "8t8Pp7q1rtyhU6FIlpRzi/P99kYNdA57VZdhGyYP+mI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\doctordetails.css", "FileLength": 13530, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "6V+pU7n++uOlt/UESW5zY0lNqWqqTBGmIibGE0z4xyc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\doctoreditdialog.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/doctoreditdialog#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "15936mmlf1", "Integrity": "+zCQX9xNPnTch9ZSz5+GyWWeyuSSyUdPE94kE2KNFRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\doctoreditdialog.css", "FileLength": 3473, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "z0wErNvWx7UyHelGxFwARSv+2viyEq4S87R3mkj1kjs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\main.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/main#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nwh3r29x55", "Integrity": "Z5ASTMeTj9tpIjyb8dzTTyWANu/tiq6RS1lyGGD7nfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\main.css", "FileLength": 11876, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "xZJm3kVCBC0AQymkAUbGm8XHNgdJUns/lyMkVaaDIbc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\mark.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/mark#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jar01ghwmm", "Integrity": "K3vaGGcmBCo/bOPijFFGkL/5YtfQUrb6gdfHOE5x+w8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\mark.svg", "FileLength": 1142, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "49vgQV6ZTVcbiX46gYy+2lbBi4Br49jBJnvQtGwvyww=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\patients.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/patients#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1ldazns5jy", "Integrity": "u92KEdvZpqnBRs5zIqLRJv28b0bFNhssxyI2DZy/CFA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\patients.css", "FileLength": 7034, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "AQfnz1TAP5ig577urpXrLadnP9iTwNOKt3iNXdPX9OU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\appoinment\\preference.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/appoinment/preference#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s61xgn8zs6", "Integrity": "RJBliwFNQ/gLIKZrQy3kYSbUQeT9r+Uv253PbsUEKa0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\appoinment\\preference.css", "FileLength": 1319, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "WyW884cBJKVtl/6JixrV1ePtfe8qodDJOOpVFg57rAU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2wlpouf80", "Integrity": "YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 155758, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "ekIxIv9/OtSb6w4ouFA3uD8zwNTgVXT8ajQIhkiGLak=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kao5znno1s", "Integrity": "xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 625953, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "3ebOc8Uz++v0FxntjxfLYxPAaSCA74cvUhjUKKUQB5E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/FONT-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z189sdz1gv", "Integrity": "+Q44zfEaCMmXduni5Td+IgCbk8sSUQwES2nWs+KKQz0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\FONT-LICENSE", "FileLength": 4017, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "zDeoFG5vS2dBht889SH2TmdoqLWvL9a3noebrm3V5eM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cmapd0fi15", "Integrity": "BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "FileLength": 9395, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "D8YCDzgA8B59OTG+cvHXBQfnKZBL29rdOxhwIl6TGEQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0uw8dim9nl", "Integrity": "OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "FileLength": 28196, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "GVVTXhmP2x4WGQDGPSCbGE02GWYvLPOXDbsWEZmczN8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wk8x8xm0ah", "Integrity": "sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "FileLength": 20996, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "8b7poMoACfLJ0ys+OGPrV9rWYiDzwOxehzMpIh+vbIg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "<PERSON><PERSON><PERSON><PERSON>", "Integrity": "oUpLdS+SoLJFwf4bzA3iKD7TCm66oLkTpAQlVJ2s1wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "FileLength": 54789, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "0Af4a7YV7kZX+tLeq9Ndi0m5ZH+QOiwwXvhvR5+n/Fw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ll5grcv8wv", "Integrity": "p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "FileLength": 28028, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "rE7NvdbAhZnZoI0mzXdCG/sQ4m9V0kNgBkKoI2jfiMQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h4d0pazwgy", "Integrity": "cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "FileLength": 14984, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "qBN/CPZXpu8SUFEtPS28lwvwy+Ut+mRJ3kJG6S6KSoM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/ICON-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3cg08az021", "Integrity": "s/Is6Ey6jfNAEfXUIOyHrXXX+RcA8hzchYnuOIWUMl4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\ICON-LICENSE", "FileLength": 1073, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "8ulxOmQw1pLVopdakwd/tsVXUBYv1WD3VOM/YPV+M/0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\open-iconic\\README.md", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/open-iconic/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l5877nzu3a", "Integrity": "9wdNXQFE78LCNHo+Hq2eXMTx+YBf2gjsufVTJc8dAV0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\README.md", "FileLength": 3501, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "95yxfHzaLyhvKUbkRMakNsMG0q5I3mFFbDbZbt6WBOI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\css\\site.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5atoypa1yv", "Integrity": "PfDlBE6l1AEmgA+hlfY6c4D8J9RV1fYt1pXhz9ArwXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 3345, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "AVlxeqmpT70nQ86uQB4CTNSHJFZ7eePBVD8mFgfHhQ4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\favicon.ico", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mjoy6y12me", "Integrity": "ws41UmW5nFdrGhq8/ZNWMXQfw5l9UZPl3rwG6waxZqo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 6574, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "184DU0P3BgAjHhuQ3cKPc8KIFRwqgEx/Dk6cL6aJetA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\index.html", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aku4ct7z8z", "Integrity": "RjzkjqQ5FUbHEzC3cmMlyjyeaUlF8pRU63v02z4519E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 3943, "LastWriteTime": "2025-07-14T20:06:44.4016935+00:00"}, "xqOKxl9uwWXbg9CHnnzJqyZ1ajFSA2pRmqEX5HYdu10=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\sample-data\\weather.json", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n3rzwp25li", "Integrity": "8/dpghKtbgJjsVb4wvShEfn+x0QUcbHkX0jArBfHj9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 544, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "YYp29ZQ2BxQaVv2zQCoSz4S8JsrbS1M4tN8UK5qZyv4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\SFResource\\blazor.polyfill.min.js", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "SFResource/blazor.polyfill.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "odyftzumiz", "Integrity": "NJv9+Wk4pbjhkcqAqO6QHeBRc6p8P333nAaBd2iC+/s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\SFResource\\blazor.polyfill.min.js", "FileLength": 111276, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "2gynKh7sa1Wqsnr3vYItnjF2dHR6cheOevXyDfKn4nE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\SFResource\\bootstrap.min.css", "SourceId": "AppointmentPlanner", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Fiverr\\AppointmentPlanner\\AppointmentPlanner\\wwwroot\\", "BasePath": "_content/AppointmentPlanner", "RelativePath": "SFResource/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dqxvahb95x", "Integrity": "hdq6Mxz3bnBf7qgSuJ5UT6FM4GqtFGlznaKTalZDMc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\SFResource\\bootstrap.min.css", "FileLength": 2264481, "LastWriteTime": "2025-06-27T04:50:09+00:00"}}, "CachedCopyCandidates": {}}