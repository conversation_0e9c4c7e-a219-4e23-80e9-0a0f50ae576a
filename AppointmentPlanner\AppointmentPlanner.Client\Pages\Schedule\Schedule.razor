﻿@page "/schedule"

@using AppointmentPlanner.Data;
@using AppointmentPlanner.Models;
@using Microsoft.JSInterop;
@implements IDisposable;

@if(isDataLoaded){
<div class="planner-calendar">
    <div class="drag-sample-wrapper droppable">
        <div class="col-lg-9 col-md-9 col-sm-9" style="padding: 0px">
            <div class="schedule-container e-droppable">
                <SfSchedule ID="schedule" TValue="Hospital" @ref="scheduleObj" Width="100%" Height="800px" EnableAutoRowHeight="true" CssClass="doctor-appointment-planner" ShowWeekend="false" StartHour="@startHour" EndHour="@endHour" @bind-SelectedDate="@currentDate" WorkDays="@workDay" FirstDayOfWeek="@calendarDayOfWeek" @bind-CurrentView="@sceduleView">
                    <ScheduleEvents TValue="Hospital" OnPopupOpen="OnPopupOpen" OnActionBegin="OnActionBegin"></ScheduleEvents>
                    <ScheduleWorkHours Start="08:00" End="21:00"></ScheduleWorkHours>
                    <ScheduleEventSettings DataSource="@scheduleEventData" ResourceColorField="@resourceColorField" AllowEditFollowingEvents="@editUpcoming">
                        <ScheduleField>
                            <FieldSubject Name="Name"></FieldSubject>
                            <FieldStartTime Name="StartTime" Title="From"></FieldStartTime>
                            <FieldEndTime Name="EndTime" Title="To"></FieldEndTime>
                            <FieldDescription Name="Symptoms" Title="Symptoms"></FieldDescription>
                        </ScheduleField>
                    </ScheduleEventSettings>
                    <ScheduleTimeScale Enable="true" Interval="@this.viewInterval"></ScheduleTimeScale>
                    <ScheduleResources>
                        <ScheduleResource TItem="Specialization" TValue="int?" DataSource="@specialistCategory" Field="DepartmentId" Title="Department" Name="Departments" TextField="Text" IdField="DepartmentId" ColorField="Color"></ScheduleResource>
                        <ScheduleResource TItem="Doctor" TValue="int" DataSource="@resourceDataSource" Query="@queryData" Field="DoctorId" Title="Consultation" Name="Doctors" TextField="Name" IdField="Id" ColorField="Color" GroupIDField="DepartmentId" WorkDaysField="AvailableDays" StartHourField="StartHour" EndHourField="EndHour"></ScheduleResource>
                    </ScheduleResources>
                    <ScheduleViews>
                        <ScheduleView Option="@View.Day"></ScheduleView>
                        <ScheduleView Option="@View.Week"></ScheduleView>
                        <ScheduleView Option="@View.Month"></ScheduleView>
                        <ScheduleView Option="@View.TimelineDay">
                            <ScheduleViewGroup EnableCompactView="false" Resources="@resourceGroup"></ScheduleViewGroup>
                        </ScheduleView>
                        <ScheduleView Option="@View.TimelineWeek">
                            <ScheduleViewGroup EnableCompactView="false" Resources="@resourceGroup"></ScheduleViewGroup>
                        </ScheduleView>
                        <ScheduleView Option="@View.TimelineWorkWeek">
                            <ScheduleViewGroup EnableCompactView="false" Resources="@resourceGroup"></ScheduleViewGroup>
                        </ScheduleView>
                        <ScheduleView Option="@View.TimelineMonth">
                            <ScheduleViewGroup EnableCompactView="false" Resources="@resourceGroup"></ScheduleViewGroup>
                        </ScheduleView>
                    </ScheduleViews>
                    <ScheduleTemplates>
                        <DateHeaderTemplate>
                            <div class="e-custom-date-header date-text" title="@(getDateHeaderText((context as TemplateContext).Date))">@(getDateHeaderText((context as TemplateContext).Date).ToUpper())</div>
                        </DateHeaderTemplate>
                    </ScheduleTemplates>
                </SfSchedule>
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-3 treeview-container-wrapper">
            <div class="treeview-container">
                <div>
                    <SfDropDownList @ref="ddlObj" Placeholder="Choose Specialist" Width="220px" TItem="Doctor" PopupHeight="auto" DataSource="@resourceDataSource" TValue="int?" CssClass="planner-dropdown" ShowClearButton="true">
                        <DropDownListEvents TItem="Doctor" TValue="int?" ValueChange="onValueChange"></DropDownListEvents>
                        <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                        <DropDownListTemplates TItem="Doctor">
                            <ItemTemplate>
                                <div class="specialist-item">
                                    <img class="value" src="css/appoinment/assets/images/@((context as Doctor).Text).png" alt="doctor" />
                                    <div class="doctor-details">
                                        <div class="name">Dr.@((context as Doctor).Name)</div>
                                        <div class="designation">@((context as Doctor).Designation)</div>
                                    </div>
                                </div>
                            </ItemTemplate>
                            <FooterTemplate>
                                <div class="add-doctor">
                                    <div class="e-icon-add e-icons"></div><div class="add-doctor-text">
                                        <NavLink Match="NavLinkMatch.Prefix" href="@($"doctors")">Add New Doctor</NavLink>
                                    </div>
                                </div>
                            </FooterTemplate>
                        </DropDownListTemplates>
                    </SfDropDownList>
                </div>
                <div class="title-container">
                    <h2 class="title-text">Waiting List</h2>
                </div>
            <SfTreeView TValue="WaitingList" LoadOnDemand="false" CssClass="treeview-external-drag" AllowDragAndDrop="true">
                <TreeViewFieldsSettings Id="Id" Text="Name" DataSource="@waitingList"></TreeViewFieldsSettings>
                <TreeViewEvents TValue="WaitingList" OnNodeDragStop="NodeDragStop"></TreeViewEvents>
                <TreeViewTemplates TValue="WaitingList">
                    <NodeTemplate>
                        <div id="waiting">
                            <div id="waitdetails">
                                <div id="waitlist">@((context as WaitingList).Name)</div>
                                <div id="event-time">@(getEventTime((context as WaitingList)))</div>
                                <div id="waitcategory">
                                    @(getDepartmentName((context as WaitingList).DepartmentId)) - @(getTreatmentDetail((context as WaitingList)))
                                </div>
                            </div>
                            <div id="item-icon-container">
                                <span class="item-icon icon-reorder"></span>
                            </div>
                        </div>
                    </NodeTemplate>
                </TreeViewTemplates>
            </SfTreeView>
            </div>
        </div>
    </div>
</div>
}
else{
    <div class="planner-dashboard-skeleton">
        <div class="appointments-skeleton">
            <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="780px" Width="90%"></SfSkeleton><br/>
        </div>
        <div class="activities-skeleton">
            @for(int i=0; i < 10; i++)
            {
                <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="3%" Width="80%"></SfSkeleton>
                <br />
                <SfSkeleton Shape="Syncfusion.Blazor.Notifications.SkeletonType.Rectangle" Height="3%" Width="60%"></SfSkeleton>
                <br /><br />
            }
        </div>
    </div>
}

@code {

    SfSchedule<Hospital> scheduleObj { get; set; }

    [Inject]
    protected IJSRuntime JsRuntime { get; set; }
    [Inject]
    protected AppointmentService Service { get; set; }
    private bool editUpcoming = false;

    private bool openDialog { get; set; } = false;
    private int viewInterval { get; set; } = 60;
    private int calendarDayOfWeek { get; set; } = 1;
    private string resourceColorField { get; set; }
    private DateTime currentDate { get; set; }

    SfDropDownList<int?, Doctor> ddlObj { get; set; }

    private List<Hospital> scheduleEventData { get; set; }

    private string[] resourceGroup { get; set; } = new string[] { "Departments", "Doctors" };

    private string startHour { get; set; }
    private string endHour { get; set; }
    private int[] workDay { get; set; } = new int[] { 0, 1, 2, 3, 4, 5, 6 };
    private Calendar workHours { get; set; }

    private View sceduleView { get; set; }

    private List<Specialization> specialistCategory { get; set; }
    private List<Doctor> resourceDataSource { get; set; }

    private List<WaitingList> waitingList { get; set; }


    private Query queryData { get; set; }
    private List<Doctor> activeDoctorData { get; set; }
    private bool isDataLoaded;

    protected override void OnInitialized()
    {
        sceduleView = (View)Enum.Parse(typeof(View), (Service.CalendarSettings.CurrentView as string));
        currentDate = Service.StartDate;
        this.viewInterval = Service.CalendarSettings.Interval;
        this.calendarDayOfWeek = Service.CalendarSettings.FirstDayOfWeek;
        this.resourceColorField = Service.CalendarSettings.BookingColor;
        this.waitingList = Service.WaitingLists;
        this.resourceDataSource = Service.Doctors;
        this.specialistCategory = Service.Specializations;
        this.startHour = Service.CalendarSettings.Calendar.Start;
        this.endHour = Service.CalendarSettings.Calendar.End;
    }

    private string getEventTime(WaitingList data)
    {
        return Service.GetFormatDate(data.StartTime, "MMM d") + ", " + Service.GetFormatDate(data.StartTime, "h:mm tt") + "-" + Service.GetFormatDate(data.EndTime, "h:mm tt");
    }

    private string getDepartmentName(int id)
    {
        Specialization specData = this.specialistCategory.Where(item => item.DepartmentId.Equals(id)).FirstOrDefault();
        return specData.Text.ToUpper();
    }

    private string getDateHeaderText(DateTime? date)
    {
        return Service.GetFormatDate((DateTime)date, "ddd, MMMM d");
    }

    private string getTreatmentDetail(WaitingList data)
    {
        if (data.Treatment != null)
        {
            return data.Treatment;
        }
        else
        {
            return "CHECKUP";
        }
    }


    public void OnPopupOpen(PopupOpenEventArgs<Hospital> args)
    {
        args.Cancel = args.Type == PopupType.QuickInfo && args.Data.Id < 1;
    }

    public void OnActionBegin(Syncfusion.Blazor.Schedule.ActionEventArgs<Hospital> args)
    {
        if (args.ActionType == ActionType.EventCreate && args.AddedRecords.Count > 0)
        {
            foreach(Hospital Data in args.AddedRecords)
            {
                Random random = new Random();
                Data.PatientId = Data.PatientId == 0 ? random.Next(1, Service.Patients.Max(p => p.Id)) : Data.PatientId;
            }
        }
    }
    private void onValueChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, Doctor> args)
    {
        if (args.Value != null)
        {
            refreshDataSource((int)args.ItemData.DepartmentId, args.ItemData.Id.ToString());
        }
        else
        {
            setDefaultData();
        }
    }
    private void refreshDataSource(int deptId, string doctorId)
    {
        List<Doctor> filterItem = Service.Doctors.Where(item => item.Id.Equals(Int32.Parse(doctorId))).ToList();
        activeDoctorData = filterItem;
        workDay = filterItem[0].AvailableDays;
        workHours = new Calendar { Start = filterItem[0].StartHour, End = filterItem[0].EndHour };
        queryData = new Query().Where(new WhereFilter() { Field = "Id", Operator = "equal", value = Int32.Parse(doctorId) });
        if (filterItem.Any())
        {
            updateBreakHours(scheduleObj.SelectedDate);
            scheduleEventData = generateEvents(activeDoctorData[0]);
        }
        else
        {
            scheduleEventData = Service.Hospitals;
        }
        this.updateWaitingList((int)deptId);
    }

    private void updateWaitingList(int? deptId)
    {
        var filteredData = Service.WaitingLists;
        if (deptId != null)
        {
            filteredData = filteredData.Where(item => item.DepartmentId.Equals(deptId)).ToList();
        }
        waitingList = filteredData;
    }

    private void setDefaultData()
    {
        specialistCategory = Service.Specializations;
        resourceDataSource = Service.Doctors;

        queryData = new Query();

        scheduleEventData = Service.Hospitals;
        updateWaitingList(null);
        startHour = Service.CalendarSettings.Calendar.Start;
        endHour = Service.CalendarSettings.Calendar.End;
        workDay = new int[] { 0, 1, 2, 3, 4, 5, 6 };
        workHours = Service.CalendarSettings.Calendar;
        activeDoctorData = new List<Doctor>();
    }
    private List<Hospital> generateEvents(Doctor activeData)
    {
        List<Hospital> filteredEvents = new List<Hospital>();

        List<Hospital> datas = Service.Hospitals.Where(item => item.DoctorId == activeData.Id).ToList();

        datas.ForEach((ele) => filteredEvents.Add(ele));

        activeData.WorkDays.ForEach((ele) =>
        {
            if (ele.State != "RemoveBreak")
            {
                int id = (filteredEvents.Any()) ? filteredEvents.Select(item => item.Id).Max() + 1 : 0;

                filteredEvents.Add(new Hospital() { Id = id, Name = "Break Time", StartTime = (DateTime)ele.BreakStartHour, EndTime = (DateTime)ele.BreakEndHour, DoctorId = activeData.Id });
            }
            if (ele.Enable)
            {
                string shiftValue = (string)activeData.DutyTiming;
                List<Hospital> createData = new List<Hospital>();
                var workend = (DateTime)ele.WorkEndHour;
                var workstart = (DateTime)ele.WorkStartHour;
                if (shiftValue == "Shift1")
                {
                    createData.Add(new Hospital() { StartTime = new DateTime(workstart.Year, workstart.Month, workstart.Day, 17, 0, 0), EndTime = new DateTime(workend.Year, workend.Month, workend.Day, 21, 0, 0) });
                }
                else if (shiftValue == "Shift2")
                {
                    createData.Add(new Hospital() { StartTime = new DateTime(workstart.Year, workstart.Month, workstart.Day, 8, 0, 0), EndTime = new DateTime(workend.Year, workend.Month, workend.Day, 10, 0, 0) });
                    createData.Add(new Hospital() { StartTime = new DateTime(workstart.Year, workstart.Month, workstart.Day, 19, 0, 0), EndTime = new DateTime(workend.Year, workend.Month, workend.Day, 21, 0, 0) });
                }
                else
                {
                    createData.Add(new Hospital() { StartTime = new DateTime(workstart.Year, workstart.Month, workstart.Day, 8, 0, 0), EndTime = new DateTime(workend.Year, workend.Month, workend.Day, 12, 0, 0) });
                }
                createData.ForEach((item) =>
                {
                    int id = filteredEvents.Select(data => data.Id).Max() + 1;

                    filteredEvents.Add(new Hospital() { Id = id, Name = "Off Work", IsBlock = true, StartTime = (DateTime)item.StartTime, EndTime = (DateTime)item.EndTime, DoctorId = activeData.Id });
                });
            }
        });
        return filteredEvents;
    }
    private void updateBreakHours(DateTime currentDate)
    {
        List<DateTime> currentViewDates = new List<DateTime>();
        DateTime firstDayOfWeek = Service.GetWeekFirstDate(currentDate);
        DateTime startDate = firstDayOfWeek;
        DateTime endDate = firstDayOfWeek.AddDays(7);
        do
        {
            currentViewDates.Add(startDate);
            startDate = startDate.AddDays(1);
        } while (startDate.AddDays(startDate.Day) != endDate.AddDays(endDate.Day));
        currentViewDates.ForEach((item) =>
        {
            activeDoctorData[0].WorkDays.ForEach((dayItem) =>
            {
                DateTime dd = (DateTime)dayItem.BreakStartHour;

                if (dd.DayOfWeek == item.DayOfWeek)
                {
                    dayItem.BreakStartHour = resetDateValue((DateTime)dayItem.BreakStartHour, item);
                    dayItem.BreakEndHour = resetDateValue((DateTime)dayItem.BreakEndHour, item);
                    dayItem.WorkStartHour = resetDateValue((DateTime)dayItem.WorkStartHour, item);
                    dayItem.WorkEndHour = resetDateValue((DateTime)dayItem.WorkEndHour, item);
                }
            });
        });
    }
    private DateTime resetDateValue(DateTime date, DateTime item)
    {
        return date = item;
    }
    public async void NodeDragStop(DragAndDropEventArgs args)
    {
        args.Cancel = true;
        CellClickEventArgs cellData = await scheduleObj.GetTargetCellAsync((int)args.Left, (int)args.Top);
        if (cellData != null)
        {
            var resourceDetails = scheduleObj.GetResourceByIndex(cellData.GroupIndex);
            WaitingList TreeData = waitingList.Where(data => data.Id.ToString() == args.DraggedNodeData.Id).First();
            Hospital eventData;
            if (resourceDetails != null)
            {
                eventData = new Hospital
                {
                    Name = TreeData.Name,
                    StartTime = cellData.StartTime,
                    EndTime = cellData.EndTime,
                    IsAllDay = cellData.IsAllDay,
                    Symptoms = TreeData.Disease,
                    PatientId = TreeData.PatientId,
                    DepartmentId = resourceDetails.GroupData.DepartmentId,
                    DoctorId = resourceDetails.GroupData.DoctorId
                };
            }
            else
            {
                eventData = new Hospital
                {
                    Name = TreeData.Name,
                    StartTime = cellData.StartTime,
                    EndTime = cellData.EndTime,
                    IsAllDay = cellData.IsAllDay,
                    Symptoms = TreeData.Disease,
                    PatientId = TreeData.PatientId,
                    DepartmentId = TreeData.DepartmentId,
                    DoctorId = Int32.Parse(args.DraggedNodeData.Id)
                };
            }
            if (TreeData != null)
            {
                waitingList.Remove(TreeData);
            }
            await this.scheduleObj.OpenEditorAsync(eventData, CurrentAction.Add);
        }
    }

    public void Dispose()
    {
        scheduleObj = null;
        ddlObj = null;
    }

    private async void LoadData()
    {   
        this.scheduleEventData = Service.Hospitals;
        this.workHours = Service.CalendarSettings.Calendar;
        this.queryData = new Query();
        await Task.Delay(100);
        isDataLoaded = true;
        StateHasChanged();
    }

    protected override void OnAfterRender(bool firstRender)
    {
    if (firstRender)
        {
        LoadData();
        }
    }
}