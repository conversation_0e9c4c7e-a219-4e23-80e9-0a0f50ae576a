﻿<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google Tag Manager -->
    <script>
        (function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-WLQL39J');</script>
    <!-- End Google Tag Manager -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="Appointment Planner is a SPA for demonstrating Syncfusion Blazor WebAssembly Components features in a real-world scenario with dashboard like layout." />
    <title>Appointment Planner | Blazor WebAssembly showcase app | Syncfusion</title>
    <base href="/" />
    <link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link href="css/site.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap.css" rel="stylesheet" />
    <!--<link href="SFResource/bootstrap.min.css" rel="stylesheet" />-->
    <link href="css/appoinment/main.css" rel="stylesheet" />
    <link href="css/appoinment/common.css" rel="stylesheet" />
    <link href="css/appoinment/dashboard.css" rel="stylesheet" />
    <link href="css/appoinment/doctor.css" rel="stylesheet" />
    <link href="css/appoinment/doctordetails.css" rel="stylesheet" />
    <link href="css/appoinment/patients.css" rel="stylesheet" />
    <link href="css/appoinment/preference.css" rel="stylesheet" />
    <link href="css/appoinment/calendar.css" rel="stylesheet" />
    <link href="css/appoinment/doctoreditdialog.css" rel="stylesheet" />
    <link href="css/appoinment/about.css" rel="stylesheet" />
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        if (/MSIE \d|Trident.*rv:/.test(navigator.userAgent)) {
            document.write('<script src="SFResource/blazor.polyfill.min.js"><\/script>');
        }
        window.checkBrowserDevice = function () {
            return (navigator.userAgent.match(/Android/i)
                || navigator.userAgent.match(/webOS/i)
                || navigator.userAgent.match(/iPhone/i)
                || navigator.userAgent.match(/iPad/i)
                || navigator.userAgent.match(/iPod/i)
                || navigator.userAgent.match(/BlackBerry/i)
                || navigator.userAgent.match(/Windows Phone/i)) ? true : false;
        }
    </script>
    <HeadOutlet @rendermode="@InteractiveWebAssembly" />
</head>

<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WLQL39J" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <Routes @rendermode="@InteractiveWebAssembly" />
    <div hidden id="sync-analytics" data-queue="Syncfusion - Showcase - Blazor - AppointmentPlanner"></div>
    <script src="_framework/blazor.web.js"></script>
</body>

</html>
