@inherits LayoutComponentBase

<div class="auth-layout">
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <span class="clinic-image icon-logo"></span>
                <h1 class="clinic-name">APPOINTMENT PLANNER</h1>
            </div>
        </div>
        
        <div class="auth-content">
            @Body
        </div>
        
        <div class="auth-footer">
            <p>&copy; 2024 Appointment Planner. All rights reserved.</p>
        </div>
    </div>
</div>

<style>
    .auth-layout {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .auth-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 450px;
        overflow: hidden;
    }

    .auth-header {
        background: #4a90e2;
        color: white;
        padding: 30px 20px;
        text-align: center;
    }

    .auth-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .clinic-name {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .icon-logo {
        width: 40px;
        height: 40px;
        background: url('/css/appoinment/assets/images/logo.png') no-repeat center;
        background-size: contain;
    }

    .auth-content {
        padding: 40px 30px;
    }

    .auth-footer {
        background: #f8f9fa;
        padding: 15px 20px;
        text-align: center;
        border-top: 1px solid #e9ecef;
    }

    .auth-footer p {
        margin: 0;
        color: #6c757d;
        font-size: 0.875rem;
    }

    @@media (max-width: 576px) {
        .auth-container {
            margin: 10px;
            max-width: none;
        }
        
        .auth-content {
            padding: 30px 20px;
        }
        
        .clinic-name {
            font-size: 1.25rem;
        }
    }
</style>
