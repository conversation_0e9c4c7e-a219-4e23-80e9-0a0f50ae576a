using AppointmentPlanner.Client.Models;
using System.Net.Http.Json;

namespace AppointmentPlanner.Client.Services
{
    public class AuthService
    {
        private readonly HttpClient _httpClient;

        public AuthService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<AuthResult> LoginAsync(string email, string password)
        {
            try
            {
                var loginModel = new LoginModel
                {
                    Email = email,
                    Password = password
                };

                var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginModel);
                
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<AuthResult>();
                    return result ?? new AuthResult { IsSuccess = false, Message = "Invalid response" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new AuthResult 
                    { 
                        IsSuccess = false, 
                        Message = response.StatusCode == System.Net.HttpStatusCode.Unauthorized 
                            ? "Invalid email or password" 
                            : "Lo<PERSON> failed. Please try again." 
                    };
                }
            }
            catch (Exception ex)
            {
                return new AuthResult 
                { 
                    IsSuccess = false, 
                    Message = "Network error. Please check your connection and try again." 
                };
            }
        }

        public async Task<AuthResult> RegisterAsync(RegisterModel model)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/auth/register", model);
                
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<AuthResult>();
                    return result ?? new AuthResult { IsSuccess = false, Message = "Invalid response" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new AuthResult 
                    { 
                        IsSuccess = false, 
                        Message = "Registration failed. Please try again." 
                    };
                }
            }
            catch (Exception ex)
            {
                return new AuthResult 
                { 
                    IsSuccess = false, 
                    Message = "Network error. Please check your connection and try again." 
                };
            }
        }
    }
}
