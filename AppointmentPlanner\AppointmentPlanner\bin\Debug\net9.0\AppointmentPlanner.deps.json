{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"AppointmentPlanner/1.0.0": {"dependencies": {"AppointmentPlanner.Client": "1.0.0", "Microsoft.AspNetCore.Components.WebAssembly.Server": "9.0.6"}, "runtime": {"AppointmentPlanner.dll": {}}}, "Microsoft.AspNetCore.Authorization/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.6", "Microsoft.AspNetCore.Components.Analyzers": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6", "Microsoft.AspNetCore.Components.Forms": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "Microsoft.JSInterop": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.JSInterop.WebAssembly": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Metadata/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.JSInterop/9.0.6": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.JSInterop.WebAssembly/9.0.6": {"dependencies": {"Microsoft.JSInterop": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Syncfusion.Blazor.Buttons/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Buttons.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Calendars/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Lists": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Calendars.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Charts/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DataVizCommon": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35", "Syncfusion.PdfExport.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Charts.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Core/26.1.35": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.6", "Syncfusion.Licensing": "26.1.35", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Core.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Data/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Data.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.DataVizCommon/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DataVizCommon.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.DropDowns/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Notifications": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DropDowns.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Grid/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Calendars": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35", "Syncfusion.PdfExport.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Grids.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Inputs/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.Blazor.SplitButtons": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Inputs.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Lists/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Lists.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Navigations/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Lists": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Navigations.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Notifications/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Notifications.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Popups/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Popups.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Schedule/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Calendars": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Schedule.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Spinner/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Spinner.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.SplitButtons/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.SplitButtons.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Blazor.Themes/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.Blazor.Themes.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.ExcelExport.Net.Core/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.ExcelExport.Net.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.Licensing/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "Syncfusion.PdfExport.Net.Core/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.PdfExport.Net.dll": {"assemblyVersion": "26.1.35.0", "fileVersion": "26.1.35.0"}}}, "System.Text.Json/9.0.6": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "AppointmentPlanner.Client/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": "9.0.6", "Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Charts": "26.1.35", "Syncfusion.Blazor.Grid": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Notifications": "26.1.35", "Syncfusion.Blazor.Schedule": "26.1.35", "Syncfusion.Blazor.Themes": "26.1.35", "System.Text.Json": "9.0.6"}, "runtime": {"AppointmentPlanner.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AppointmentPlanner/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-FG/fHZAg2J9NKt+y2BH+fMjc+TqA7wlri/3PNtoSgvclMlr5iPshDLXHQgvS9l7IUzzfkqQ3/DWPYuA4OJu75A==", "path": "microsoft.aspnetcore.authorization/9.0.6", "hashPath": "microsoft.aspnetcore.authorization.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-4WzUBnpxqm0Gvv3XX47h5g09LN4dtQpJjYW2LAfoaVeho5TLM00uDj2YHzjxQwcRTdrG7tZDCyf4V4wwRpjGcA==", "path": "microsoft.aspnetcore.components/9.0.6", "hashPath": "microsoft.aspnetcore.components.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-UXMjbezwMMoL21L20OB1BLGF9M1BFPzdWiezaiZSBG55z36BEXvs8hetKw7PiuytOdUqWFI1wkPd3MD9jpeKAw==", "path": "microsoft.aspnetcore.components.analyzers/9.0.6", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GHdFdrU+9ePkuE7S2g7iu+Lg8FPBY9qGaK+FPSBHNx8ulEdrOHJhufb1FZH/m++NoJgcikOzJ14+eL96HfBr8g==", "path": "microsoft.aspnetcore.components.forms/9.0.6", "hashPath": "microsoft.aspnetcore.components.forms.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-c+2B1DdgAOKXt+mMcZgeQPbdoW0Ro+783m+yPK/QQHhjV8cbkPBWXg6OMJC2Vzum5tfAC3coNhlmcLUUBwc3ZQ==", "path": "microsoft.aspnetcore.components.web/9.0.6", "hashPath": "microsoft.aspnetcore.components.web.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-w0n0ge6caIRDhMn03/OGkBWJPFOrf/VS4UNSdbm7hibbAG9Y9oFUnkY6DN+b3WgXcjprSep1VypqC83dzGskXw==", "path": "microsoft.aspnetcore.components.webassembly/9.0.6", "hashPath": "microsoft.aspnetcore.components.webassembly.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-e8Y0+dSBpsfdYXUgBCHrzAeFywRFa5aog7O8l/VFwSAQCJJlcOKOus/RGRVHTLBARGdHfr9guSdMtSYXjFGOJg==", "path": "microsoft.aspnetcore.components.webassembly.server/9.0.6", "hashPath": "microsoft.aspnetcore.components.webassembly.server.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-SZAmFKGsQPP/xyDir96J5UNTAdCsfeCpPwFO9EewIcgQ8gUr3KHmn4CTC2HmHIDdiTujGC2T3VBcSxmrY+3n/g==", "path": "microsoft.aspnetcore.metadata/9.0.6", "hashPath": "microsoft.aspnetcore.metadata.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "path": "microsoft.extensions.configuration.json/9.0.6", "hashPath": "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-1HJ<PERSON>bwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.JSInterop/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IS9tI2pnpeoX+d4CO4FuTsm+6uymanPGSpBhHefTfMNIwBUvwLIcXch4su0QEW4MSslYdDJ9yzz2+OuIUuI5lw==", "path": "microsoft.jsinterop/9.0.6", "hashPath": "microsoft.jsinterop.9.0.6.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-y93mMbFyYo5lq9cDkm2wYlob7RmeKulZVpjMYUOIztry/TpXlHrFgBb2TcWMuKnwvfbf4Ag6oIx+F3aQAAVA6A==", "path": "microsoft.jsinterop.webassembly/9.0.6", "hashPath": "microsoft.jsinterop.webassembly.9.0.6.nupkg.sha512"}, "Syncfusion.Blazor.Buttons/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-sky/UkU6X/9Jf7bPyv/ltYmrLGhzttv3zbw0o0F/+76Hr/A0TNXn4Bv6fCP0eR3o4qTm7i9EH9qXWbcp5Oz5JQ==", "path": "syncfusion.blazor.buttons/26.1.35", "hashPath": "syncfusion.blazor.buttons.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Calendars/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-AmchDdNrcOtLvvXLXxVVrBpbE3jJlezzlRnw8GBe0zE2vd2fOllfYgjHPKzQywrZqDTALF/REjBXhRn4PyRLeA==", "path": "syncfusion.blazor.calendars/26.1.35", "hashPath": "syncfusion.blazor.calendars.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Charts/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-j1uEzmu1JEI6xSh0TMWWjmvusGdE5VPKHLpm1hwVd9KmN5jKMvXmHmmDOEkyozYJNDQ6DaPWkDYCMJuC/waLSg==", "path": "syncfusion.blazor.charts/26.1.35", "hashPath": "syncfusion.blazor.charts.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-aQDiHkPJLAqn4qJDXM7b0fk9+D4+mbyWmlOl4NJ4YrXhMoy2QB0yWCv6QnHkPS6PZmOeqCByybT69LW/rm5x5g==", "path": "syncfusion.blazor.core/26.1.35", "hashPath": "syncfusion.blazor.core.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Data/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-MzTSFeTLYrEjr9ONbS9qzUKaE0554CKAsKHRcY2dj3H+vkJf7XChHLZhswWkQlkyrLR7LlMe5TCxK/LiNJ7adg==", "path": "syncfusion.blazor.data/26.1.35", "hashPath": "syncfusion.blazor.data.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.DataVizCommon/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-I01SzntfWFPsju0kurhSgPs+mZAZXIDwKyyXVJdqOYRgEsBZqTksWRHTBjpc8PLI0r+QQd213F9Pa5/8sSZ+9A==", "path": "syncfusion.blazor.datavizcommon/26.1.35", "hashPath": "syncfusion.blazor.datavizcommon.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.DropDowns/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-MmIzaF3yeqlqLXuMNiLgW5XmQHyV6PLmPVZ9gIBq9ILTacPxiMYqbXTYAGFDQDAnDpsxe6QTZqgqXAD+Sex1Tg==", "path": "syncfusion.blazor.dropdowns/26.1.35", "hashPath": "syncfusion.blazor.dropdowns.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Grid/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-keqIJLIfCxbXbNo0aSn4ZdB0AfcBV1R8JDyNTMcjBugGXJu0nW9s6bbc8iy1u67xAmmvYvFhAANbtzdg8ASvvg==", "path": "syncfusion.blazor.grid/26.1.35", "hashPath": "syncfusion.blazor.grid.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Inputs/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-QD9CtUriUEDzfU1psaZdhoSSZu5toRlkJCqXJ4VeDaJ424fIU5yxR3QH148gGC19bwCpMraJ2nfUYznwhWgpOQ==", "path": "syncfusion.blazor.inputs/26.1.35", "hashPath": "syncfusion.blazor.inputs.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Lists/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-jTyeyGf/7j+U3sHshjN8ASj+xAiy4xZLqFTQbIlrv4vrloXU9nZJ4oqZdT420Rp7d3O8iQ66akXytUD61c/sTw==", "path": "syncfusion.blazor.lists/26.1.35", "hashPath": "syncfusion.blazor.lists.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Navigations/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-zoxCVQBdcVBMfIc2grIXX9w7UjbcWoHSiGGn+suzzNuGUknHoO3oTQJWUWjZO4MtCziSsooy0DdmuWaNsBKgFQ==", "path": "syncfusion.blazor.navigations/26.1.35", "hashPath": "syncfusion.blazor.navigations.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Notifications/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-UjpQvqsvELyqRYu6mKyqUowds54Xdmsh796cmRIOo4hHKq4ruH7N4Nr5Wdu6EI9AHcHlr9WMo8Ti3Dtz/nntcg==", "path": "syncfusion.blazor.notifications/26.1.35", "hashPath": "syncfusion.blazor.notifications.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Popups/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-SHrSVZr/ahrbBfe+i+8uh3YiFvQwAYrcH2tqV2HQDvjJwDhjRt0lLuLjl1CzzQFSF4/CDFHKsoF2eZ8CC5N5dw==", "path": "syncfusion.blazor.popups/26.1.35", "hashPath": "syncfusion.blazor.popups.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Schedule/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-G0TF7QtCPlpCAklIYocsWNmAqrp065U2m5W7kqslpUGbBMRDyvSFOveMT84rXv/Ropa3x7Q8Seshzaxiu4wECg==", "path": "syncfusion.blazor.schedule/26.1.35", "hashPath": "syncfusion.blazor.schedule.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Spinner/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-KabpF4Y2dvtlIQ+EEkipwMVOaIqeDhHg+mBtjEvUWXOdy2jwum5KH0oz2C6cJoaDcPyUTGbNdqZ5i/9KUN8W5A==", "path": "syncfusion.blazor.spinner/26.1.35", "hashPath": "syncfusion.blazor.spinner.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.SplitButtons/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-aFAkkpybPrrvFhskAjCsgVftC1Qj3vOUFH8JvnnwjyeRCAlcDLIkOoJgPjjyf/SulFWkMxMb2/seKCM3KkHoiQ==", "path": "syncfusion.blazor.splitbuttons/26.1.35", "hashPath": "syncfusion.blazor.splitbuttons.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Themes/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-sG0M2hw1WnCj0jnDQd6WNQrhjcX27XMY8eK/MMK6M9mDv6cSy2WE76WiYrg9k7GPwkKkwU5ijTeoolkiHc9GSQ==", "path": "syncfusion.blazor.themes/26.1.35", "hashPath": "syncfusion.blazor.themes.26.1.35.nupkg.sha512"}, "Syncfusion.ExcelExport.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-s0XLmtU2Q+nvjik4fTSxPseaWKS36YNRU59c+Hn8Ion6hPia71gOrWaanwux1l7LOpSAOGH8/TFZVyvLc83PPA==", "path": "syncfusion.excelexport.net.core/26.1.35", "hashPath": "syncfusion.excelexport.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.Licensing/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-DPMS7ODbzaS+S2//uOMW8XL/08AEIMz/TEb113gaXdwga328ozz+4wiQGZBJzjLGglyO1vV+GTUigquDGHWcFg==", "path": "syncfusion.licensing/26.1.35", "hashPath": "syncfusion.licensing.26.1.35.nupkg.sha512"}, "Syncfusion.PdfExport.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-/mqe7z37o/cHmSAjTY+jtjF2gKkL230U+Nk6hjn5tR9INBET2AbhiqIdE/jMduIQ+NqZplcOj5S1bq65jMgq+w==", "path": "syncfusion.pdfexport.net.core/26.1.35", "hashPath": "syncfusion.pdfexport.net.core.26.1.35.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}, "AppointmentPlanner.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}