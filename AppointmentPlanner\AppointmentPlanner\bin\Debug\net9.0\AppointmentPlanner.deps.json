{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"AppointmentPlanner/1.0.0": {"dependencies": {"AppointmentPlanner.Client": "1.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.AspNetCore.Components.WebAssembly.Server": "9.0.6", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.6", "Microsoft.AspNetCore.Identity.UI": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Design": "9.0.6", "Microsoft.EntityFrameworkCore.Tools": "9.0.6", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.2", "System.IdentityModel.Tokens.Jwt": "8.2.1"}, "runtime": {"AppointmentPlanner.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Authorization/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.6", "Microsoft.AspNetCore.Components.Analyzers": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {}, "Microsoft.AspNetCore.Components.Authorization/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.6", "Microsoft.AspNetCore.Components": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6", "Microsoft.AspNetCore.Components.Forms": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "Microsoft.JSInterop": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.JSInterop.WebAssembly": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Identity.Stores": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Identity.UI/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.6", "Microsoft.Extensions.Identity.Stores": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.UI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Metadata/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.Design/9.0.6": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.6"}}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyModel/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Identity.Core/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Identity.Stores/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Identity.Core": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.IdentityModel.Abstractions/8.2.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.Logging/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.1.51115"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.2.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.2.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.1.51115"}}}, "Microsoft.JSInterop/9.0.6": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.JSInterop.WebAssembly/9.0.6": {"dependencies": {"Microsoft.JSInterop": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "Npgsql/9.0.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.2.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Npgsql": "9.0.2"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.2.0"}}}, "Syncfusion.Blazor.Buttons/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Buttons.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Calendars/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Lists": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Calendars.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Charts/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DataVizCommon": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35", "Syncfusion.PdfExport.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Charts.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Core/26.1.35": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.6", "Syncfusion.Licensing": "26.1.35", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Core.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Data/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Data.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.DataVizCommon/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DataVizCommon.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.DropDowns/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Notifications": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DropDowns.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Grid/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Calendars": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35", "Syncfusion.PdfExport.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Grids.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Inputs/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.Blazor.SplitButtons": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Inputs.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Lists/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Lists.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Navigations/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Lists": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Navigations.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Notifications/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Notifications.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Popups/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Popups.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Schedule/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Calendars": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Data": "26.1.35", "Syncfusion.Blazor.DropDowns": "26.1.35", "Syncfusion.Blazor.Inputs": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35", "Syncfusion.ExcelExport.Net.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Schedule.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Spinner/26.1.35": {"dependencies": {"Syncfusion.Blazor.Core": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Spinner.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.SplitButtons/26.1.35": {"dependencies": {"Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Core": "26.1.35", "Syncfusion.Blazor.Popups": "26.1.35", "Syncfusion.Blazor.Spinner": "26.1.35"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.SplitButtons.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Themes/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.Blazor.Themes.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.ExcelExport.Net.Core/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.ExcelExport.Net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Licensing/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.PdfExport.Net.Core/26.1.35": {"runtime": {"lib/net8.0/Syncfusion.PdfExport.Net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IdentityModel.Tokens.Jwt/8.2.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.2.1", "Microsoft.IdentityModel.Tokens": "8.2.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.1.51115"}}}, "System.IO.Pipelines/7.0.0": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Json/9.0.6": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.Channels/7.0.0": {}, "AppointmentPlanner.Client/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Authorization": "9.0.6", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.6", "Syncfusion.Blazor.Buttons": "26.1.35", "Syncfusion.Blazor.Charts": "26.1.35", "Syncfusion.Blazor.Grid": "26.1.35", "Syncfusion.Blazor.Navigations": "26.1.35", "Syncfusion.Blazor.Notifications": "26.1.35", "Syncfusion.Blazor.Schedule": "26.1.35", "Syncfusion.Blazor.Themes": "26.1.35", "System.IdentityModel.Tokens.Jwt": "8.2.1", "System.Text.Json": "9.0.6"}, "runtime": {"AppointmentPlanner.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AppointmentPlanner/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nH1mrzr77pk+n1E5+A/0KlzkNhqy3LS3gUGEjJf0PQE6PZAc3pr8rLwUATcaJMr/12qsxHT+kcvRZMxc4bxFpA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.6", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-FG/fHZAg2J9NKt+y2BH+fMjc+TqA7wlri/3PNtoSgvclMlr5iPshDLXHQgvS9l7IUzzfkqQ3/DWPYuA4OJu75A==", "path": "microsoft.aspnetcore.authorization/9.0.6", "hashPath": "microsoft.aspnetcore.authorization.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-4WzUBnpxqm0Gvv3XX47h5g09LN4dtQpJjYW2LAfoaVeho5TLM00uDj2YHzjxQwcRTdrG7tZDCyf4V4wwRpjGcA==", "path": "microsoft.aspnetcore.components/9.0.6", "hashPath": "microsoft.aspnetcore.components.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-UXMjbezwMMoL21L20OB1BLGF9M1BFPzdWiezaiZSBG55z36BEXvs8hetKw7PiuytOdUqWFI1wkPd3MD9jpeKAw==", "path": "microsoft.aspnetcore.components.analyzers/9.0.6", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Authorization/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-6Iwar7bMjSZBJbK7nrZ+pYyMBKYtZEBX1F4ZlYtidYHOiHoRcoFlgV3Y3+dkoLhaVnhyMaQzuoV5v75nfe5GCg==", "path": "microsoft.aspnetcore.components.authorization/9.0.6", "hashPath": "microsoft.aspnetcore.components.authorization.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GHdFdrU+9ePkuE7S2g7iu+Lg8FPBY9qGaK+FPSBHNx8ulEdrOHJhufb1FZH/m++NoJgcikOzJ14+eL96HfBr8g==", "path": "microsoft.aspnetcore.components.forms/9.0.6", "hashPath": "microsoft.aspnetcore.components.forms.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-c+2B1DdgAOKXt+mMcZgeQPbdoW0Ro+783m+yPK/QQHhjV8cbkPBWXg6OMJC2Vzum5tfAC3coNhlmcLUUBwc3ZQ==", "path": "microsoft.aspnetcore.components.web/9.0.6", "hashPath": "microsoft.aspnetcore.components.web.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-w0n0ge6caIRDhMn03/OGkBWJPFOrf/VS4UNSdbm7hibbAG9Y9oFUnkY6DN+b3WgXcjprSep1VypqC83dzGskXw==", "path": "microsoft.aspnetcore.components.webassembly/9.0.6", "hashPath": "microsoft.aspnetcore.components.webassembly.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-e8Y0+dSBpsfdYXUgBCHrzAeFywRFa5aog7O8l/VFwSAQCJJlcOKOus/RGRVHTLBARGdHfr9guSdMtSYXjFGOJg==", "path": "microsoft.aspnetcore.components.webassembly.server/9.0.6", "hashPath": "microsoft.aspnetcore.components.webassembly.server.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-AbRZPnAXFIvKKOWer6setdYy6/KY/hM9onf/LscD1O6sD8AJSyYemxmIa9T/kbfEPek2neEn9+lnPf7ClfglTQ==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.6", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-8yJVwMyycWClrTjbyWl9AUquizaMC9wZWj/Q+Pqdbse25cwMSb6fROrpZyZULqRfE67iV1vh6igG3S+nakIKqQ==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.6", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-50+a2SQEx0eOQmLPjxO0sy/leDNvwsU1/HFXpTbIjXGUURrL5fCZGlTENM3CJNWzwd2cCGB/ZLFHFgKTdSyLzw==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.6", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-UeM/54cDvN67I6jD2k8PaMyafHE2J0AGz7H8JOqKCziTdgaqdQeVS9/IAOi/CUIKMh6NZ+Y2pk/VykwBF8toug==", "path": "microsoft.aspnetcore.identity.ui/9.0.6", "hashPath": "microsoft.aspnetcore.identity.ui.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-SZAmFKGsQPP/xyDir96J5UNTAdCsfeCpPwFO9EewIcgQ8gUr3KHmn4CTC2HmHIDdiTujGC2T3VBcSxmrY+3n/g==", "path": "microsoft.aspnetcore.metadata/9.0.6", "hashPath": "microsoft.aspnetcore.metadata.9.0.6.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-6xabdZH2hOqSocjDIOd0FZLslH7kDX8ODY4lBR298GwkAkxuItjNgZHuRbTi9hmfDS2Hh02r+d17Fa8XT4lKLQ==", "path": "microsoft.entityframeworkcore.design/9.0.6", "hashPath": "microsoft.entityframeworkcore.design.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "path": "microsoft.entityframeworkcore.relational/9.0.6", "hashPath": "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Qs+OnYPOrfb5wpSXNGdm9v3QattLhou26xamaICIE9jqWAW7xdzDlY/yXRz6zKFLnzRH70IM+XXYVElEVeQ9Q==", "path": "microsoft.entityframeworkcore.tools/9.0.6", "hashPath": "microsoft.entityframeworkcore.tools.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "path": "microsoft.extensions.configuration.json/9.0.6", "hashPath": "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "path": "microsoft.extensions.dependencymodel/9.0.6", "hashPath": "microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-KyXroxnOI6ZpCth3pW718Fn4hwtvgjjLCKRJg43UFR/5Wfo0ZKVGUJGnQT3+fTkoyLCElJUKwIHezY6IjOXz/g==", "path": "microsoft.extensions.fileproviders.embedded/9.0.6", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-1HJ<PERSON>bwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-UByIcYE2r8k2k8Mh+Me9qFg5jzJQ8OFe5r1bePqvI15iK118PsjUtJXppFp3miuZpMOMpYKZM9xfLZ/64MhcNg==", "path": "microsoft.extensions.identity.core/9.0.6", "hashPath": "microsoft.extensions.identity.core.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-kAPyVejLK5/hNSXRdJ3LZomkzwsde2dEklLGVhR7OS+fJs70nIuj5f52U0LDQ05KAuQsKOTc/utaghiH07uKIg==", "path": "microsoft.extensions.identity.stores/9.0.6", "hashPath": "microsoft.extensions.identity.stores.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-8sMlmHhh5HdP3+yCSCUpJpN1yYrJ6J/V39df9siY8PeMckRMrSBRL/TMs/Jex6P1ly/Ie2mFqvhcPHHrNmCd/w==", "path": "microsoft.identitymodel.abstractions/8.2.1", "hashPath": "microsoft.identitymodel.abstractions.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-Oo0SBOzK6p3YIUcc1YTJCaYezVUa5HyUJ/AAB35QwxhhD6Blei5tNjNYDR0IbqHdb5EPUIiKcIbQGoj2b1mIbg==", "path": "microsoft.identitymodel.jsonwebtokens/8.2.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-EgSEAtBoWBynACdhKnMlVAFGGWqOIdmbpW7Vvx2SQ7u7ogZ50NcEGSoGljEsQoGIRYpo0UxXYktKcYMp+G/Bcg==", "path": "microsoft.identitymodel.logging/8.2.1", "hashPath": "microsoft.identitymodel.logging.8.2.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-oQeLWCATuVXOCdIvouM4GG2xl1YNng+uAxYwu7CG6RuW+y+1+slXrOBq5csTU2pnV2SH3B1GmugDf6Jv/lexjw==", "path": "microsoft.identitymodel.tokens/8.2.1", "hashPath": "microsoft.identitymodel.tokens.8.2.1.nupkg.sha512"}, "Microsoft.JSInterop/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IS9tI2pnpeoX+d4CO4FuTsm+6uymanPGSpBhHefTfMNIwBUvwLIcXch4su0QEW4MSslYdDJ9yzz2+OuIUuI5lw==", "path": "microsoft.jsinterop/9.0.6", "hashPath": "microsoft.jsinterop.9.0.6.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-y93mMbFyYo5lq9cDkm2wYlob7RmeKulZVpjMYUOIztry/TpXlHrFgBb2TcWMuKnwvfbf4Ag6oIx+F3aQAAVA6A==", "path": "microsoft.jsinterop.webassembly/9.0.6", "hashPath": "microsoft.jsinterop.webassembly.9.0.6.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Npgsql/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hCbO8box7i/********************************+eAknvBNhhUHeJVGAQo44sySZTfdVffp4BrtPeLZOAA==", "path": "npgsql/9.0.2", "hashPath": "npgsql.9.0.2.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-cYdOGplIvr9KgsG8nJ8xnzBTImeircbgetlzS1OmepS5dAQW6PuGpVrLOKBNEwEvGYZPsV8037X5vZ/Dmpwz7Q==", "path": "npgsql.entityframeworkcore.postgresql/9.0.2", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.2.nupkg.sha512"}, "Syncfusion.Blazor.Buttons/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-sky/UkU6X/9Jf7bPyv/ltYmrLGhzttv3zbw0o0F/+76Hr/A0TNXn4Bv6fCP0eR3o4qTm7i9EH9qXWbcp5Oz5JQ==", "path": "syncfusion.blazor.buttons/26.1.35", "hashPath": "syncfusion.blazor.buttons.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Calendars/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-AmchDdNrcOtLvvXLXxVVrBpbE3jJlezzlRnw8GBe0zE2vd2fOllfYgjHPKzQywrZqDTALF/REjBXhRn4PyRLeA==", "path": "syncfusion.blazor.calendars/26.1.35", "hashPath": "syncfusion.blazor.calendars.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Charts/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-j1uEzmu1JEI6xSh0TMWWjmvusGdE5VPKHLpm1hwVd9KmN5jKMvXmHmmDOEkyozYJNDQ6DaPWkDYCMJuC/waLSg==", "path": "syncfusion.blazor.charts/26.1.35", "hashPath": "syncfusion.blazor.charts.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-aQDiHkPJLAqn4qJDXM7b0fk9+D4+mbyWmlOl4NJ4YrXhMoy2QB0yWCv6QnHkPS6PZmOeqCByybT69LW/rm5x5g==", "path": "syncfusion.blazor.core/26.1.35", "hashPath": "syncfusion.blazor.core.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Data/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-MzTSFeTLYrEjr9ONbS9qzUKaE0554CKAsKHRcY2dj3H+vkJf7XChHLZhswWkQlkyrLR7LlMe5TCxK/LiNJ7adg==", "path": "syncfusion.blazor.data/26.1.35", "hashPath": "syncfusion.blazor.data.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.DataVizCommon/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-I01SzntfWFPsju0kurhSgPs+mZAZXIDwKyyXVJdqOYRgEsBZqTksWRHTBjpc8PLI0r+QQd213F9Pa5/8sSZ+9A==", "path": "syncfusion.blazor.datavizcommon/26.1.35", "hashPath": "syncfusion.blazor.datavizcommon.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.DropDowns/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-MmIzaF3yeqlqLXuMNiLgW5XmQHyV6PLmPVZ9gIBq9ILTacPxiMYqbXTYAGFDQDAnDpsxe6QTZqgqXAD+Sex1Tg==", "path": "syncfusion.blazor.dropdowns/26.1.35", "hashPath": "syncfusion.blazor.dropdowns.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Grid/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-keqIJLIfCxbXbNo0aSn4ZdB0AfcBV1R8JDyNTMcjBugGXJu0nW9s6bbc8iy1u67xAmmvYvFhAANbtzdg8ASvvg==", "path": "syncfusion.blazor.grid/26.1.35", "hashPath": "syncfusion.blazor.grid.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Inputs/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-QD9CtUriUEDzfU1psaZdhoSSZu5toRlkJCqXJ4VeDaJ424fIU5yxR3QH148gGC19bwCpMraJ2nfUYznwhWgpOQ==", "path": "syncfusion.blazor.inputs/26.1.35", "hashPath": "syncfusion.blazor.inputs.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Lists/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-jTyeyGf/7j+U3sHshjN8ASj+xAiy4xZLqFTQbIlrv4vrloXU9nZJ4oqZdT420Rp7d3O8iQ66akXytUD61c/sTw==", "path": "syncfusion.blazor.lists/26.1.35", "hashPath": "syncfusion.blazor.lists.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Navigations/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-zoxCVQBdcVBMfIc2grIXX9w7UjbcWoHSiGGn+suzzNuGUknHoO3oTQJWUWjZO4MtCziSsooy0DdmuWaNsBKgFQ==", "path": "syncfusion.blazor.navigations/26.1.35", "hashPath": "syncfusion.blazor.navigations.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Notifications/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-UjpQvqsvELyqRYu6mKyqUowds54Xdmsh796cmRIOo4hHKq4ruH7N4Nr5Wdu6EI9AHcHlr9WMo8Ti3Dtz/nntcg==", "path": "syncfusion.blazor.notifications/26.1.35", "hashPath": "syncfusion.blazor.notifications.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Popups/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-SHrSVZr/ahrbBfe+i+8uh3YiFvQwAYrcH2tqV2HQDvjJwDhjRt0lLuLjl1CzzQFSF4/CDFHKsoF2eZ8CC5N5dw==", "path": "syncfusion.blazor.popups/26.1.35", "hashPath": "syncfusion.blazor.popups.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Schedule/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-G0TF7QtCPlpCAklIYocsWNmAqrp065U2m5W7kqslpUGbBMRDyvSFOveMT84rXv/Ropa3x7Q8Seshzaxiu4wECg==", "path": "syncfusion.blazor.schedule/26.1.35", "hashPath": "syncfusion.blazor.schedule.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Spinner/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-KabpF4Y2dvtlIQ+EEkipwMVOaIqeDhHg+mBtjEvUWXOdy2jwum5KH0oz2C6cJoaDcPyUTGbNdqZ5i/9KUN8W5A==", "path": "syncfusion.blazor.spinner/26.1.35", "hashPath": "syncfusion.blazor.spinner.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.SplitButtons/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-aFAkkpybPrrvFhskAjCsgVftC1Qj3vOUFH8JvnnwjyeRCAlcDLIkOoJgPjjyf/SulFWkMxMb2/seKCM3KkHoiQ==", "path": "syncfusion.blazor.splitbuttons/26.1.35", "hashPath": "syncfusion.blazor.splitbuttons.26.1.35.nupkg.sha512"}, "Syncfusion.Blazor.Themes/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-sG0M2hw1WnCj0jnDQd6WNQrhjcX27XMY8eK/MMK6M9mDv6cSy2WE76WiYrg9k7GPwkKkwU5ijTeoolkiHc9GSQ==", "path": "syncfusion.blazor.themes/26.1.35", "hashPath": "syncfusion.blazor.themes.26.1.35.nupkg.sha512"}, "Syncfusion.ExcelExport.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-s0XLmtU2Q+nvjik4fTSxPseaWKS36YNRU59c+Hn8Ion6hPia71gOrWaanwux1l7LOpSAOGH8/TFZVyvLc83PPA==", "path": "syncfusion.excelexport.net.core/26.1.35", "hashPath": "syncfusion.excelexport.net.core.26.1.35.nupkg.sha512"}, "Syncfusion.Licensing/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-DPMS7ODbzaS+S2//uOMW8XL/08AEIMz/TEb113gaXdwga328ozz+4wiQGZBJzjLGglyO1vV+GTUigquDGHWcFg==", "path": "syncfusion.licensing/26.1.35", "hashPath": "syncfusion.licensing.26.1.35.nupkg.sha512"}, "Syncfusion.PdfExport.Net.Core/26.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-/mqe7z37o/cHmSAjTY+jtjF2gKkL230U+Nk6hjn5tR9INBET2AbhiqIdE/jMduIQ+NqZplcOj5S1bq65jMgq+w==", "path": "syncfusion.pdfexport.net.core/26.1.35", "hashPath": "syncfusion.pdfexport.net.core.26.1.35.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-GVQmbjr2N8awFWPTWyThLxgKnFINObG1P+oX7vFrBY8um3V7V7Dh3wnxaGxNH6v6lSTeVQrY+SaUUBX9H3TPcw==", "path": "system.identitymodel.tokens.jwt/8.2.1", "hashPath": "system.identitymodel.tokens.jwt.8.2.1.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "AppointmentPlanner.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}