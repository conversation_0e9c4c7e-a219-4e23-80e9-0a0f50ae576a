using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AppointmentPlanner.Data.Models
{
    public class DoctorProfile
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Education { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Specialization { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Experience { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Designation { get; set; }

        [StringLength(50)]
        public string? DutyTiming { get; set; }

        [StringLength(20)]
        public string? Availability { get; set; } = "Available";

        [StringLength(10)]
        public string? StartHour { get; set; }

        [StringLength(10)]
        public string? EndHour { get; set; }

        [StringLength(7)]
        public string? Color { get; set; } = "#1aaa55";

        public int DepartmentId { get; set; }

        public string? AvailableDays { get; set; } // JSON array of available days

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        public virtual Department Department { get; set; } = null!;

        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    }
}
