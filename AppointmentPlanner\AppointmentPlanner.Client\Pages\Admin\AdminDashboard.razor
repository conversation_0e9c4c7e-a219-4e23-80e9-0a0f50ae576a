@page "/admin/dashboard"
@attribute [Authorize(Roles = "Admin")]
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization

<PageTitle>Admin Dashboard - Appointment Planner</PageTitle>

<div class="admin-dashboard">
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-shield-alt"></i>
            Admin Dashboard
        </h1>
        <p class="dashboard-subtitle">System administration and management</p>
    </div>

    <div class="row">
        <!-- System Statistics -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-primary">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>@totalUsers</h3>
                    <p>Total Users</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-success">
                <div class="stat-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="stat-content">
                    <h3>@totalDoctors</h3>
                    <p>Healthcare Professionals</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-info">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-content">
                    <h3>@totalAppointments</h3>
                    <p>Total Appointments</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card stat-warning">
                <div class="stat-icon">
                    <i class="fas fa-hospital"></i>
                </div>
                <div class="stat-content">
                    <h3>@totalDepartments</h3>
                    <p>Departments</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <button class="btn btn-primary btn-action" @onclick="ManageUsers">
                            <i class="fas fa-users-cog"></i>
                            Manage Users
                        </button>
                        <button class="btn btn-success btn-action" @onclick="ManageDepartments">
                            <i class="fas fa-building"></i>
                            Manage Departments
                        </button>
                        <button class="btn btn-info btn-action" @onclick="ViewReports">
                            <i class="fas fa-chart-bar"></i>
                            View Reports
                        </button>
                        <button class="btn btn-warning btn-action" @onclick="SystemSettings">
                            <i class="fas fa-cogs"></i>
                            System Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-clock"></i>
                        Recent System Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="activity-list">
                        @foreach (var activity in recentActivities)
                        {
                            <div class="activity-item">
                                <div class="activity-icon @activity.IconClass">
                                    <i class="@activity.Icon"></i>
                                </div>
                                <div class="activity-content">
                                    <p class="activity-text">@activity.Description</p>
                                    <small class="activity-time">@activity.Timestamp.ToString("MMM dd, yyyy HH:mm")</small>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Management Preview -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-users"></i>
                        User Management
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Comprehensive user management features will be available here.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Admin Features:</strong> User registration approval, role management, system monitoring, and comprehensive reporting tools.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .admin-dashboard {
        padding: 20px;
    }

    .dashboard-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .dashboard-title {
        color: #333;
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .dashboard-title i {
        color: #dc3545;
        margin-right: 15px;
    }

    .dashboard-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .stat-primary .stat-icon { background: #007bff; }
    .stat-success .stat-icon { background: #28a745; }
    .stat-info .stat-icon { background: #17a2b8; }
    .stat-warning .stat-icon { background: #ffc107; }

    .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        color: #333;
    }

    .stat-content p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .btn-action {
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .activity-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .activity-icon.success { background: #28a745; }
    .activity-icon.info { background: #17a2b8; }
    .activity-icon.warning { background: #ffc107; }

    .activity-content {
        flex: 1;
    }

    .activity-text {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 0.9rem;
    }

    .activity-time {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 10px 10px 0 0 !important;
    }

    .card-title {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .card-title i {
        color: #007bff;
        margin-right: 10px;
    }
</style>

@code {
    private int totalUsers = 156;
    private int totalDoctors = 23;
    private int totalAppointments = 1247;
    private int totalDepartments = 6;

    private List<ActivityItem> recentActivities = new()
    {
        new ActivityItem { Description = "New doctor registered: Dr. Sarah Johnson", Icon = "fas fa-user-plus", IconClass = "success", Timestamp = DateTime.Now.AddMinutes(-15) },
        new ActivityItem { Description = "System backup completed successfully", Icon = "fas fa-database", IconClass = "info", Timestamp = DateTime.Now.AddHours(-2) },
        new ActivityItem { Description = "Department 'Cardiology' updated", Icon = "fas fa-edit", IconClass = "warning", Timestamp = DateTime.Now.AddHours(-4) },
        new ActivityItem { Description = "Monthly report generated", Icon = "fas fa-file-alt", IconClass = "info", Timestamp = DateTime.Now.AddDays(-1) }
    };

    private void ManageUsers()
    {
        // Navigate to user management
    }

    private void ManageDepartments()
    {
        // Navigate to department management
    }

    private void ViewReports()
    {
        // Navigate to reports
    }

    private void SystemSettings()
    {
        // Navigate to system settings
    }

    public class ActivityItem
    {
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string IconClass { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
