﻿<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(Shared.MainLayout)">
                <NotAuthorized Context="authContext">
                    @if (authContext.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="alert alert-danger">
                            <h4>Access Denied</h4>
                            <p>You don't have permission to access this resource.</p>
                        </div>
                    }
                </NotAuthorized>
                <Authorizing>
                    <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Checking authentication...</p>
                        </div>
                    </div>
                </Authorizing>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(Shared.MainLayout)">
                <div class="alert alert-warning">
                    <h4>Page Not Found</h4>
                    <p>Sorry, there's nothing at this address.</p>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

@code {
    private class RedirectToLogin : ComponentBase
    {
        [Inject] private NavigationManager Navigation { get; set; } = default!;

        protected override void OnInitialized()
        {
            Navigation.NavigateTo("/login");
        }
    }
}
