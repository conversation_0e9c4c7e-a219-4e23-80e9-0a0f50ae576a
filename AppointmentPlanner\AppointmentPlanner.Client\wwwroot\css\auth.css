/* Authentication Styles */

.form-title {
    color: #333;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-align: center;
}

.form-subtitle {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #4a90e2;
    width: 16px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
}

.form-control:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
    outline: none;
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #4a90e2;
}

.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    margin: 0;
}

.form-check-label {
    color: #495057;
    font-size: 0.9rem;
    cursor: pointer;
}

.forgot-password-link {
    color: #4a90e2;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password-link:hover {
    color: #357abd;
    text-decoration: underline;
}

.btn-login, .btn-register {
    width: 100%;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-login:hover, .btn-register:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.btn-login:disabled, .btn-register:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.auth-divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.register-link, .login-link {
    text-align: center;
    margin-top: 1.5rem;
}

.register-link p, .login-link p {
    color: #6c757d;
    margin: 0;
}

.register-link a, .login-link a {
    color: #4a90e2;
    text-decoration: none;
    font-weight: 500;
}

.register-link a:hover, .login-link a:hover {
    text-decoration: underline;
}

/* Role Selection Styles */
.role-selection {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.role-option {
    position: relative;
}

.role-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.role-label {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.role-label:hover {
    border-color: #4a90e2;
    background: #f8f9ff;
}

.role-option input[type="radio"]:checked + .role-label {
    border-color: #4a90e2;
    background: #f8f9ff;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.role-label i {
    color: #4a90e2;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.role-label span {
    font-weight: 500;
    color: #333;
}

.role-label small {
    display: block;
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Professional Fields */
.professional-fields {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.section-title {
    color: #333;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* Demo Accounts */
.demo-accounts {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1.5rem;
    text-align: center;
}

.demo-accounts h6 {
    color: #495057;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.demo-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.demo-buttons .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .role-selection {
        gap: 0.75rem;
    }
    
    .role-label {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .role-label i {
        font-size: 1.25rem;
    }
    
    .demo-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .demo-buttons .btn {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 576px) {
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .professional-fields {
        padding: 1rem;
    }
}
