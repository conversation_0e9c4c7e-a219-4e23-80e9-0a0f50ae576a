﻿.planner-dashboard {
    display: flex;
    margin: 50px 75px;
    font-size: 14px;
    letter-spacing: 0.38px;
    height: 100%;
}

.planner-dashboard .view-detail-display {
    width: 100%;
}

.planner-dashboard .view-detail-display .e-card.day-events-container .label-text,
.planner-dashboard .view-detail-display .e-card.week-events-container .label-text {
    font-weight: 600;
    font-size: 16px;
    color: #333;
    letter-spacing: 0.48px;
    display: flex;
    padding: 10px 12px 6.5px 12px;
}

.planner-dashboard .e-card .e-card-content {
    padding: 0px;
}

.planner-dashboard .view-detail-display .grid-container {
    height: 270px;
}

.planner-dashboard .text-display {
    text-align: center;
    line-height: 30px;
}

.planner-dashboard .view-detail-display .grid-container .e-card-header {
    height: 60px;
}

.planner-dashboard .view-detail-display .grid-container .e-card-header .e-card-header-caption .e-card-header-title {
    display: flex;
    padding: 0px;
    font-weight: 600;
}

.planner-dashboard .view-detail-display .grid-container .e-card-header .e-card-header-caption .label-text {
    flex: auto;
    letter-spacing: 0.48px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: .5rem 0;
}

.planner-dashboard .view-detail-display .grid-container .link-text {
    flex: initial;
    font-size: 13px;
    color: #5851e1;
    letter-spacing: 0.39px;
    text-align: right;
    text-overflow: ellipsis;
    overflow: hidden;
}

.planner-dashboard .view-detail-display .grid-container .link-text a {
    cursor: pointer;
    text-decoration: underline;
    text-overflow: ellipsis;
    overflow: hidden;
}

.planner-dashboard header {
    display: none;
}

.planner-dashboard .view-detail-display .e-card.day-events-container,
.planner-dashboard .view-detail-display .e-card.week-events-container {
    background: #fff;
    box-shadow: 1px 2px 20px 0 rgba(173, 173, 255, 0.20);
}

.planner-dashboard .e-card {
    border: 0px;
}


.planner-dashboard .view-detail-display .e-card .week-event-count,
.planner-dashboard .view-detail-display .e-card .day-event-count {
    font-weight: 400;
    font-size: 36px;
    cursor: default;
    color: #5851e1;
    letter-spacing: 1.08px;
    height: 44px;
    display: flex;
    justify-content: center;
    flex-basis: 50%;
}

.planner-dashboard .view-detail-display .e-card .count-container {
    display: flex;
    margin: 0 16px 6.3px 17.4px;
}

.planner-dashboard .view-detail-display .e-card .icon-week img,
.planner-dashboard .view-detail-display .e-card .icon-day img {
    width: 57px;
    height: 56.2px;
}

.planner-dashboard .view-detail-display .chart-container,
.planner-dashboard .view-detail-display .grid-container {
    margin-top: 30px;
    box-shadow: 1px 2px 20px 0 rgba(173, 173, 255, 0.20);
}

.planner-dashboard .view-detail-display .chart-container {
    width: 100%;
    height: 340px;
}

.planner-dashboard .view-detail-display .chart-container text#chartcontainer_ChartTitle {
    fill: #333;
    font-weight: 600;
    font-size: 16px;
    color: #333 !important;
    letter-spacing: 0.48px;
    padding: 10px 16px;
}

.planner-dashboard .list-display {
    padding-left: 0px;
}

.planner-dashboard .list-display .recent-activity,
.planner-dashboard .list-display .doctor-availability {
    background-clip: padding-box;
    box-sizing: border-box;
    overflow: hidden;
    background: #fff;
    box-shadow: 1px 2px 20px 0 rgba(173, 173, 255, 0.20);
    height: 375px;
}

.planner-dashboard .list-display .recent-activity {
    margin-bottom: 30px;
    width: 100%;
}

/*activity listview*/

.activity-template .e-list-header {
    border-bottom: 0;
    font-weight: 600;
    font-size: 16px;
    color: #333 !important;
    letter-spacing: 0.48px;
    padding: 10px 16px;
}

.activity-template .e-list-container .e-list-item {
    border: none;
    height: 100%;
    padding: 0px 16px;
    margin-bottom: 10.2px;
    line-height: 18px;
}

.activity-template .e-list-container .e-list-item .activity-container {
    padding: 5px 0;
}

.activity-template .e-list-container .e-list-item .activity-container.doctor {
    border-left: 2px solid #e16651;
}

.activity-template .e-list-container .e-list-item .activity-container.appointment {
    border-left: 2px solid #5851e1;
}

.activity-template .e-list-container .e-list-item .activity-container.patient {
    border-left: 2px solid #51e1b7;
}

.activity-template .e-list-container .e-list-item .activity-container .activity-message {
    font-weight: 400;
    font-size: 13px;
    color: #333;
    letter-spacing: 0.39px;
    line-height: 18px;
    height: auto;
    margin-left: 12px;
    padding-bottom: 5px;
}

.activity-template .e-list-container .e-list-item .activity-container .activity-message .type-name {
    font-weight: bold;
}

.activity-template .e-list-container .e-list-item .activity-container .activity-time {
    font-size: 13px;
    color: #999;
    letter-spacing: 0.39px;
    line-height: 18px;
    width: 254px;
    height: 20.1px;
    margin-left: 12px;
}


/*doctor availability*/

.e-list-template .e-list-header {
    border-bottom: 0;
    flex: auto;
    font-weight: 600;
    font-size: 16px;
    color: #333 !important;
    letter-spacing: 0.48px;
    padding: 10px 16px;
}

.e-list-template .e-list-header .e-headertemplate-text {
    width: 100%;
}

.e-list-template .e-list-header .e-headertemplate-text .availability-title {
    display: flex;
}

.e-list-template .e-list-header .e-headertemplate-text .availability-title .header-text {
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.e-list-template .e-list-header .e-headertemplate-text .availability-title .all-text {
    flex: initial;
    font-weight: 400;
    font-size: 13px;
    color: #5851e1;
    letter-spacing: 0.39px;
    text-align: right;
    margin-right: 4px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.e-list-template .e-list-container,
.activity-template .e-list-container {
    padding-top: 10px;
}

.e-list-template .e-list-container li.e-list-item {
    border: none;
    display: flex;
    padding: 5px 16px;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container {
    display: flex;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container {
    display: flex;
    padding-left: 1px;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image {
    padding: 5px 0;
    position: relative;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image img {
    height: 32px;
    width: 32px;
    border-radius: 16px;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image .availability.busy,
.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image .availability.available,
.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image .availability.away {
    display: inline-block;
    border: 2px solid #fff;
    border-radius: 16px;
    height: 14px;
    width: 14px;
    position: absolute;
    top: 47%;
    left: 65%;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image .availability.busy {
    background: #d40000;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image .availability.away {
    background: #ff8c2e;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .image-container .doctor-image .availability.available {
    background: #00d406;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .detail-container {
    display: grid;
    padding-left: 16px;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .detail-container .doctor-name {
    font-weight: 500;
    font-size: 13px;
    color: #666;
    letter-spacing: 0.39px;
    padding: 3px 0;
}

.e-list-template .e-list-container li.e-list-item .availabilty-container .detail-container .doctor-speciality {
    font-size: 10px;
    color: #666;
    letter-spacing: 0.3px;
    padding: 2px 0;
}

.e-tooltip-wrap {
    border-radius: 15px 15px 15px 0;
    opacity: 0.8;
    background: #333;
}

.e-tooltip-wrap .e-tip-content {
    border-radius: 15px 15px 15px 0;
    font-size: 13px;
    color: #fff;
    letter-spacing: 0.39px;
    padding: 5px 8px;
}

.view-detail-display .col-lg-8,
.view-detail-display .col-md-8,
.view-detail-display .col-sm-8 {
    padding-right: 20px;
    padding-left: 0px;
}

.view-detail-display a {
    text-decoration: underline;
    color: -webkit-link;
}

.e-listview .e-list-header .e-text {
    cursor: initial;
    text-indent: 0;
}

@media (max-width: 850px) {
    .planner-dashboard {
        margin: 20px 10px;
    }
}