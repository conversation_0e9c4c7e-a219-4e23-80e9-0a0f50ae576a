﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.themes\26.1.35\buildTransitive\Syncfusion.Blazor.Themes.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.themes\26.1.35\buildTransitive\Syncfusion.Blazor.Themes.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.core\26.1.35\buildTransitive\Syncfusion.Blazor.Core.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.core\26.1.35\buildTransitive\Syncfusion.Blazor.Core.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.spinner\26.1.35\buildTransitive\Syncfusion.Blazor.Spinner.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.spinner\26.1.35\buildTransitive\Syncfusion.Blazor.Spinner.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.buttons\26.1.35\buildTransitive\Syncfusion.Blazor.Buttons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.buttons\26.1.35\buildTransitive\Syncfusion.Blazor.Buttons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.popups\26.1.35\buildTransitive\Syncfusion.Blazor.Popups.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.popups\26.1.35\buildTransitive\Syncfusion.Blazor.Popups.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\26.1.35\buildTransitive\Syncfusion.Blazor.SplitButtons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\26.1.35\buildTransitive\Syncfusion.Blazor.SplitButtons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.notifications\26.1.35\buildTransitive\Syncfusion.Blazor.Notifications.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.notifications\26.1.35\buildTransitive\Syncfusion.Blazor.Notifications.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.data\26.1.35\buildTransitive\Syncfusion.Blazor.Data.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.data\26.1.35\buildTransitive\Syncfusion.Blazor.Data.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.lists\26.1.35\buildTransitive\Syncfusion.Blazor.Lists.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.lists\26.1.35\buildTransitive\Syncfusion.Blazor.Lists.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.inputs\26.1.35\buildTransitive\Syncfusion.Blazor.Inputs.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.inputs\26.1.35\buildTransitive\Syncfusion.Blazor.Inputs.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.dropdowns\26.1.35\buildTransitive\Syncfusion.Blazor.DropDowns.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.dropdowns\26.1.35\buildTransitive\Syncfusion.Blazor.DropDowns.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.navigations\26.1.35\buildTransitive\Syncfusion.Blazor.Navigations.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.navigations\26.1.35\buildTransitive\Syncfusion.Blazor.Navigations.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.calendars\26.1.35\buildTransitive\Syncfusion.Blazor.Calendars.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.calendars\26.1.35\buildTransitive\Syncfusion.Blazor.Calendars.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.schedule\26.1.35\buildTransitive\Syncfusion.Blazor.Schedule.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.schedule\26.1.35\buildTransitive\Syncfusion.Blazor.Schedule.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.grid\26.1.35\buildTransitive\Syncfusion.Blazor.Grid.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.grid\26.1.35\buildTransitive\Syncfusion.Blazor.Grid.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.charts\26.1.35\buildTransitive\Syncfusion.Blazor.Charts.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.charts\26.1.35\buildTransitive\Syncfusion.Blazor.Charts.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.sdk.webassembly.pack\9.0.5\build\Microsoft.NET.Sdk.WebAssembly.Pack.props" Condition="Exists('$(NuGetPackageRoot)microsoft.net.sdk.webassembly.pack\9.0.5\build\Microsoft.NET.Sdk.WebAssembly.Pack.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.illink.tasks\9.0.5\build\Microsoft.NET.ILLink.Tasks.props" Condition="Exists('$(NuGetPackageRoot)microsoft.net.illink.tasks\9.0.5\build\Microsoft.NET.ILLink.Tasks.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly\9.0.6\build\net9.0\Microsoft.AspNetCore.Components.WebAssembly.props" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly\9.0.6\build\net9.0\Microsoft.AspNetCore.Components.WebAssembly.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_NET_Sdk_WebAssembly_Pack Condition=" '$(PkgMicrosoft_NET_Sdk_WebAssembly_Pack)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.net.sdk.webassembly.pack\9.0.5</PkgMicrosoft_NET_Sdk_WebAssembly_Pack>
    <PkgMicrosoft_NET_ILLink_Tasks Condition=" '$(PkgMicrosoft_NET_ILLink_Tasks)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.net.illink.tasks\9.0.5</PkgMicrosoft_NET_ILLink_Tasks>
  </PropertyGroup>
</Project>