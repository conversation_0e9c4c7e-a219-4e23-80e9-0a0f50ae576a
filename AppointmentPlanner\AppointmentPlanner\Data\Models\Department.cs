using System.ComponentModel.DataAnnotations;

namespace AppointmentPlanner.Data.Models
{
    public class Department
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(7)]
        public string Color { get; set; } = "#1aaa55";

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<DoctorProfile> Doctors { get; set; } = new List<DoctorProfile>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    }
}
