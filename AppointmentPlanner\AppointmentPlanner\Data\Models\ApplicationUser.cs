using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace AppointmentPlanner.Data.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(20)]
        public override string? PhoneNumber { get; set; }

        public DateTime DateOfBirth { get; set; }

        [StringLength(10)]
        public string? Gender { get; set; }

        [StringLength(20)]
        public string? BloodGroup { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<Appointment> PatientAppointments { get; set; } = new List<Appointment>();
        public virtual ICollection<Appointment> DoctorAppointments { get; set; } = new List<Appointment>();
        public virtual DoctorProfile? DoctorProfile { get; set; }

        // Computed properties
        public string FullName => $"{FirstName} {LastName}";
    }

    public enum UserRole
    {
        Admin,
        Professional, // Doctor/Healthcare Professional
        Client        // Patient/Client
    }
}
