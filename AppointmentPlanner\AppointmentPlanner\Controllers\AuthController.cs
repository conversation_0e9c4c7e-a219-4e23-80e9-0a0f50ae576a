using Microsoft.AspNetCore.Mvc;
using AppointmentPlanner.Services;
using AppointmentPlanner.Models;

namespace AppointmentPlanner.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpPost("register")]
        public async Task<ActionResult<AuthResult>> Register([FromBody] RegisterModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new AuthResult 
                    { 
                        Success = false, 
                        Message = "Invalid model data" 
                    });
                }

                var result = await _authService.RegisterAsync(model);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration");
                return StatusCode(500, new AuthResult 
                { 
                    Success = false, 
                    Message = "An error occurred during registration" 
                });
            }
        }

        [HttpPost("login")]
        public async Task<ActionResult<AuthResult>> Login([FromBody] LoginModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new AuthResult 
                    { 
                        Success = false, 
                        Message = "Invalid model data" 
                    });
                }

                var result = await _authService.LoginAsync(model);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return Unauthorized(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user login");
                return StatusCode(500, new AuthResult 
                { 
                    Success = false, 
                    Message = "An error occurred during login" 
                });
            }
        }

        [HttpPost("logout")]
        public async Task<ActionResult> Logout()
        {
            try
            {
                var result = await _authService.LogoutAsync();
                
                if (result)
                {
                    return Ok(new { message = "Logout successful" });
                }
                
                return BadRequest(new { message = "Logout failed" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { message = "An error occurred during logout" });
            }
        }

        [HttpPost("forgot-password")]
        public async Task<ActionResult> ForgotPassword([FromBody] ForgotPasswordModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { message = "Invalid email address" });
                }

                var result = await _authService.SendPasswordResetEmailAsync(model.Email);
                
                // Always return success for security reasons (don't reveal if email exists)
                return Ok(new { message = "If the email exists, a password reset link has been sent" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset request");
                return StatusCode(500, new { message = "An error occurred" });
            }
        }

        [HttpPost("reset-password")]
        public async Task<ActionResult<AuthResult>> ResetPassword([FromBody] ResetPasswordModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new AuthResult 
                    { 
                        Success = false, 
                        Message = "Invalid model data" 
                    });
                }

                var result = await _authService.ResetPasswordAsync(model);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset");
                return StatusCode(500, new AuthResult 
                { 
                    Success = false, 
                    Message = "An error occurred during password reset" 
                });
            }
        }
    }
}
