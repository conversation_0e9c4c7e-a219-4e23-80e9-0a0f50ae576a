﻿.e-btn.e-primary {
    background-color: #7575ff !important;
    border-color: #4242ff !important;
}

.e-btn.e-primary:hover {
        background-color: #4242ff !important;
}


/*skeleton*/
.planner-dashboard-skeleton {
    display: flex;
    margin: 50px 62px;
    height: 100%;
    width: 100%;
}

.appointments-skeleton {
    margin: 10px;
    width: 100%;
    height: 100%;
    padding-top: 1.1rem;
}

.activities-skeleton {
    margin: 10px;
    width: 100%;
    padding-top: 1.1rem;
    padding-right: 50px;
}

@media (max-width: 575px) {
    .planner-dashboard-skeleton {
        margin: 0px 0px;
    }

    .appointments-skeleton {
        display: none;
    }

    .activities-skeleton {
        margin: 30px 0px 30px 30px;
        width: 100%;
        padding-right: 0;
        margin-left: 31px;
    }
}

.device-skeleton {
    display: none;
}

@media (max-width: 575px) {
    .device-skeleton {
        display: flex;
    }
}

/*Message component's style*/
.deleted-msg {
    width: 100%;
    letter-spacing: 0.5px;
}

.e-message .e-msg-content {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.e-popup.e-popup-open.e-dialog {
    max-height: 98% !important;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled),
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) {
    border-color: #7575ff !important;
}

.e-input-group:active:not(.e-warning):not(.e-error):not(.e-disabled),
.e-input-group.e-control-wrapper:active:not(.e-warning):not(.e-error):not(.e-disabled) {
    border-color: #7575ff !important;
}

.e-dropdownbase .e-list-item.e-active {
    background-color: #7575ff !important;
}
